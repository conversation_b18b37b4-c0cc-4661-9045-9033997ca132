import wutheringWavesAPI from "../components/WutheringWavesAPI.js"

// 测试配置 - 使用用户提供的最新token
const realToken = "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY3NjA3MzMyLCJ1c2VySWQiOjEwODE2NzMyfQ.JBD_vTlMHMWgrptBvTOI05uUYFIdYITsfK9x1bTVHcw,6F02FE7B671ACA64694F19FB67EBEBAD07659846"
const realUid = "100381247"

console.log("🔍 简单Token验证测试...")
console.log(`📋 使用Token: ${realToken.substring(0, 50)}...`)
console.log(`🎮 使用UID: ${realUid}`)
console.log("================================================================================")

async function testSimpleTokenValidation() {
  try {
    console.log("📋 测试1：直接调用getBaseInfo（不预先验证token）")
    console.log("==================================================")
    
    try {
      const baseResult = await wutheringWavesAPI.getBaseInfo(realUid, realToken)
      if (baseResult && baseResult.success) {
        console.log("✅ getBaseInfo成功")
        console.log(`📊 数据大小: ${JSON.stringify(baseResult.data).length} 字符`)
        console.log(`📊 数据预览: ${JSON.stringify(baseResult.data).substring(0, 200)}...`)
      } else {
        console.log("❌ getBaseInfo失败:", baseResult?.message || "未知错误")
      }
    } catch (error) {
      console.log("❌ getBaseInfo异常:", error.message)
    }
    
    console.log("\n📋 测试2：检查token格式和解析")
    console.log("==================================================")
    
    // 分析token格式
    const tokenParts = realToken.split(',')
    console.log(`🔍 Token部分数量: ${tokenParts.length}`)
    if (tokenParts.length >= 2) {
      console.log(`🔍 JWT部分: ${tokenParts[0].substring(0, 50)}...`)
      console.log(`🔍 附加部分: ${tokenParts[1]}`)
      
      // 尝试解析JWT
      try {
        const jwtParts = tokenParts[0].split('.')
        if (jwtParts.length === 3) {
          const payload = JSON.parse(atob(jwtParts[1]))
          console.log(`🔍 JWT载荷:`, payload)
          
          // 检查token是否过期
          const now = Date.now()
          const created = payload.created
          const ageHours = (now - created) / (1000 * 60 * 60)
          console.log(`🔍 Token创建时间: ${new Date(created).toLocaleString()}`)
          console.log(`🔍 Token年龄: ${ageHours.toFixed(2)} 小时`)
        }
      } catch (error) {
        console.log("❌ JWT解析失败:", error.message)
      }
    }
    
    console.log("\n📋 测试3：测试不同的API调用模式")
    console.log("==================================================")
    
    // 测试一个简单的API调用，看看是否能成功
    try {
      console.log("🔄 尝试调用getRoleInfo...")
      const roleResult = await wutheringWavesAPI.getRoleInfo(realUid, realToken)
      if (roleResult && roleResult.success) {
        console.log("✅ getRoleInfo成功")
        console.log(`📊 角色数量: ${roleResult.data?.roleList?.length || 0}`)
      } else {
        console.log("❌ getRoleInfo失败:", roleResult?.message || "未知错误")
      }
    } catch (error) {
      console.log("❌ getRoleInfo异常:", error.message)
    }
    
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message)
  }
}

// 运行测试
testSimpleTokenValidation()
  .then(() => {
    console.log("\n✨ 简单Token验证测试完成！")
    console.log("💡 请检查token是否有效以及API调用是否成功")
  })
  .catch(error => {
    console.error("❌ 测试过程中发生错误:", error)
  })
