import axios from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';

// 测试配置
const TEST_CONFIG = {
    uid: '100381247',
    token: 'eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c',
    proxy: 'http://1907581050:<EMAIL>:9151'
};

// 基于 axios 的 API 实现
class AxiosBasedKuroApi {
    constructor() {
        this.proxyAgent = new HttpsProxyAgent(TEST_CONFIG.proxy);
    }

    generateRandomString(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    getWutheringWavesUIDHeaders(platform = 'ios') {
        const devCode = this.generateRandomString();
        const headers = {
            'source': platform,
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'devCode': devCode,
            'version': '2.5.0',
            'token': TEST_CONFIG.token
        };

        if (platform === 'ios') {
            headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0';
        } else {
            headers['User-Agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********';
        }

        return headers;
    }

    async getBatToken(uid, token) {
        try {
            const headers = this.getWutheringWavesUIDHeaders('ios');
            headers['b-at'] = ''; // 清空 b-at
            
            const data = {
                serverId: '76402e5b20be2c39f095a152090afddc',
                roleId: uid
            };

            const response = await axios.post(
                'https://api.kurobbs.com/aki/roleBox/requestToken',
                data,
                {
                    headers,
                    httpsAgent: this.proxyAgent,
                    timeout: 10000
                }
            );

            if (response.data.code === 200 || response.data.code === 10902) {
                let accessToken = '';
                const contentData = response.data.data;
                
                if (typeof contentData === 'string') {
                    try {
                        const jsonData = JSON.parse(contentData);
                        accessToken = jsonData.accessToken || '';
                    } catch (e) {
                        console.log('解析 bat token 失败:', e.message);
                    }
                } else if (typeof contentData === 'object') {
                    accessToken = contentData.accessToken || '';
                }
                
                return accessToken;
            }
            
            return null;
        } catch (error) {
            console.log('获取 bat token 失败:', error.message);
            return null;
        }
    }

    async makeExtendedAPIRequest(url, data, batToken) {
        const headers = this.getWutheringWavesUIDHeaders('ios');
        
        // 添加 bat token
        if (batToken) {
            headers['b-at'] = batToken;
        }

        try {
            const response = await axios.post(url, data, {
                headers,
                httpsAgent: this.proxyAgent,
                timeout: 10000
            });

            return response.data;
        } catch (error) {
            if (error.response) {
                return error.response.data;
            }
            throw error;
        }
    }

    async getExploreData(uid, batToken) {
        const url = 'https://api.kurobbs.com/aki/roleBox/akiBox/exploreIndex';
        const data = {
            gameId: 3,
            serverId: '76402e5b20be2c39f095a152090afddc',
            roleId: uid,
            countryCode: '1'
        };
        return await this.makeExtendedAPIRequest(url, data, batToken);
    }

    async getChallengeData(uid, batToken) {
        const url = 'https://api.kurobbs.com/aki/roleBox/akiBox/challengeDetails';
        const data = {
            gameId: 3,
            serverId: '76402e5b20be2c39f095a152090afddc',
            roleId: uid
        };
        return await this.makeExtendedAPIRequest(url, data, batToken);
    }

    async getCalabashData(uid, batToken) {
        const url = 'https://api.kurobbs.com/aki/roleBox/akiBox/calabashData';
        const data = {
            gameId: 3,
            serverId: '76402e5b20be2c39f095a152090afddc',
            roleId: uid
        };
        return await this.makeExtendedAPIRequest(url, data, batToken);
    }

    async getTowerData(uid, batToken) {
        const url = 'https://api.kurobbs.com/aki/roleBox/akiBox/towerIndex';
        const data = {
            gameId: 3,
            serverId: '76402e5b20be2c39f095a152090afddc',
            roleId: uid
        };
        return await this.makeExtendedAPIRequest(url, data, batToken);
    }

    async getMoreActivityData(uid, batToken) {
        const url = 'https://api.kurobbs.com/aki/roleBox/akiBox/moreActivity';
        const data = {
            gameId: 3,
            serverId: '76402e5b20be2c39f095a152090afddc',
            roleId: uid
        };
        return await this.makeExtendedAPIRequest(url, data, batToken);
    }
}

// 测试基于 axios 的实现
async function testAxiosBasedAPI() {
    console.log('\n=== 测试基于 axios 的扩展API实现 ===');
    console.log(`使用 UID: ${TEST_CONFIG.uid}`);
    console.log(`使用代理: ${TEST_CONFIG.proxy}`);
    
    const api = new AxiosBasedKuroApi();
    
    // 1. 获取 bat token
    console.log('\n1. 获取 bat token...');
    const batToken = await api.getBatToken(TEST_CONFIG.uid, TEST_CONFIG.token);
    if (!batToken) {
        console.log('❌ bat token 获取失败');
        return;
    }
    console.log('✅ bat token 获取成功');
    
    // 2. 测试扩展API
    const extendedAPIs = [
        { name: '探索数据', method: 'getExploreData' },
        { name: '挑战数据', method: 'getChallengeData' },
        { name: '数据坞信息', method: 'getCalabashData' },
        { name: '塔楼数据', method: 'getTowerData' },
        { name: '更多活动数据', method: 'getMoreActivityData' }
    ];
    
    console.log('\n2. 测试扩展API...');
    let successCount = 0;
    let totalCount = extendedAPIs.length;
    
    for (const apiTest of extendedAPIs) {
        try {
            console.log(`\n测试 ${apiTest.name}...`);
            
            const result = await api[apiTest.method](TEST_CONFIG.uid, batToken);
            
            if (result.code === 200 || result.code === 10902) {
                console.log(`✅ ${apiTest.name}: 成功`);
                console.log(`   响应码: ${result.code}`);
                console.log(`   消息: ${result.msg || 'N/A'}`);
                successCount++;
            } else {
                console.log(`❌ ${apiTest.name}: 失败`);
                console.log(`   响应码: ${result.code}`);
                console.log(`   消息: ${result.msg || 'N/A'}`);
            }
            
        } catch (error) {
            console.log(`❌ ${apiTest.name}: 异常`);
            console.log(`   异常信息: ${error.message}`);
        }
    }
    
    console.log(`\n=== axios 实现测试结果 ===`);
    console.log(`成功: ${successCount}/${totalCount}`);
    console.log(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
    
    if (successCount === totalCount) {
        console.log('🎉 所有扩展API测试通过！axios 实现成功避免了风险检测！');
    } else if (successCount > 0) {
        console.log('⚠️ 部分扩展API测试成功，axios 实现有所改善');
    } else {
        console.log('❌ 所有扩展API测试失败，需要进一步分析');
    }
}

// 运行测试
testAxiosBasedAPI().catch(console.error);
