import axios from "axios"
import { HttpsProxyAgent } from "https-proxy-agent"

// 测试配置 - 使用用户提供的最新token
const realToken = "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY3NjA3MzMyLCJ1c2VySWQiOjEwODE2NzMyfQ.JBD_vTlMHMWgrptBvTOI05uUYFIdYITsfK9x1bTVHcw,6F02FE7B671ACA64694F19FB67EBEBAD07659846"
const realUid = "100381247"
const proxyUrl = "http://1907581050:<EMAIL>:9151"

console.log("🔍 获取角色列表测试...")
console.log(`📋 使用Token: ${realToken.substring(0, 50)}...`)
console.log("================================================================================")

// 按照WutheringWavesUID的方式获取角色列表
async function getKuroRoleList(token, did) {
  const header = {
    "source": "ios",
    "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
    "devCode": did,
    "version": "2.5.0",
    "token": token,
  }

  const data = {
    gameId: "3"
  }

  const options = {
    method: "POST",
    url: "https://api.kurobbs.com/user/role/list",
    headers: header,
    timeout: 10000,
  }

  // 配置代理
  if (proxyUrl) {
    const agent = new HttpsProxyAgent(proxyUrl)
    options.httpsAgent = agent
    console.log(`🌐 使用代理: ${proxyUrl}`)
  }

  // 设置请求体
  const params = new URLSearchParams()
  for (const [key, value] of Object.entries(data)) {
    params.append(key, value)
  }
  options.data = params.toString()

  try {
    console.log(`📡 请求: POST ${options.url}`)
    console.log(`📋 请求头:`, JSON.stringify(header, null, 2))
    console.log(`📋 请求体:`, options.data)
    
    const response = await axios(options)
    console.log(`✅ 响应: code=${response.data.code}, msg=${response.data.msg}`)
    return response.data
  } catch (error) {
    console.error(`❌ 请求失败:`, error.message)
    if (error.response) {
      console.error(`❌ 响应状态:`, error.response.status)
      console.error(`❌ 响应数据:`, error.response.data)
    }
    throw error
  }
}

async function testGetRoleList() {
  try {
    console.log("📋 测试：获取库街区角色列表")
    console.log("==================================================")
    
    // 从token中提取did
    const tokenParts = realToken.split(',')
    const did = tokenParts.length >= 2 ? tokenParts[1] : "6F02FE7B671ACA64694F19FB67EBEBAD07659846"
    
    console.log(`🔍 使用DID: ${did}`)
    
    try {
      const roleListResult = await getKuroRoleList(realToken, did)
      
      if (roleListResult && roleListResult.code === 200 && roleListResult.data) {
        console.log("✅ 获取角色列表成功!")
        console.log(`📊 角色数量: ${roleListResult.data.length}`)
        
        // 显示所有角色信息
        roleListResult.data.forEach((role, index) => {
          console.log(`\n🎮 角色 ${index + 1}:`)
          console.log(`   - 角色ID: ${role.roleId}`)
          console.log(`   - 服务器ID: ${role.serverId}`)
          console.log(`   - 服务器名称: ${role.serverName}`)
          console.log(`   - 角色名称: ${role.roleName}`)
          console.log(`   - 等级: ${role.level}`)
          console.log(`   - 是否默认: ${role.isDefault}`)
          
          // 检查是否匹配我们的UID
          if (role.roleId === realUid) {
            console.log(`   ⭐ 这是我们要查询的角色！`)
            console.log(`   ⭐ 正确的服务器ID应该是: ${role.serverId}`)
          }
        })
        
        // 查找匹配的角色
        const matchingRole = roleListResult.data.find(role => role.roleId === realUid)
        if (matchingRole) {
          console.log(`\n🎯 找到匹配的角色:`)
          console.log(`   - 角色ID: ${matchingRole.roleId}`)
          console.log(`   - 服务器ID: ${matchingRole.serverId}`)
          console.log(`   - 服务器名称: ${matchingRole.serverName}`)
          
          // 现在用正确的serverId测试refresh_data
          console.log(`\n📋 使用正确的serverId测试refresh_data`)
          console.log("==================================================")
          
          const refreshHeader = {
            "source": "ios",
            "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
            "devCode": did,
            "version": "2.5.0",
            "did": did,
            "token": realToken,
          }
          
          // 移除roleId（按照WutheringWavesUID方式）
          delete refreshHeader.roleId
          
          const refreshData = {
            gameId: "3",
            serverId: matchingRole.serverId,
            roleId: matchingRole.roleId,
          }
          
          try {
            const refreshOptions = {
              method: "POST",
              url: "https://api.kurobbs.com/aki/roleBox/akiBox/refreshData",
              headers: refreshHeader,
              timeout: 10000,
            }
            
            if (proxyUrl) {
              const agent = new HttpsProxyAgent(proxyUrl)
              refreshOptions.httpsAgent = agent
            }
            
            const refreshParams = new URLSearchParams()
            for (const [key, value] of Object.entries(refreshData)) {
              refreshParams.append(key, value)
            }
            refreshOptions.data = refreshParams.toString()
            
            console.log(`📡 请求: POST ${refreshOptions.url}`)
            console.log(`📋 使用正确的serverId: ${matchingRole.serverId}`)
            
            const refreshResponse = await axios(refreshOptions)
            console.log(`✅ refresh_data响应: code=${refreshResponse.data.code}, msg=${refreshResponse.data.msg}`)
            
            if (refreshResponse.data.code === 200 || refreshResponse.data.code === 10902) {
              console.log("🎉 refresh_data成功！这说明我们找到了正确的配置")
            } else {
              console.log("⚠️ refresh_data仍然失败，可能还有其他问题")
            }
            
          } catch (error) {
            console.log("❌ refresh_data测试失败:", error.message)
          }
          
        } else {
          console.log(`\n❌ 未找到匹配UID ${realUid} 的角色`)
          console.log(`💡 可能的原因：`)
          console.log(`   1. UID不正确`)
          console.log(`   2. 该角色未绑定到当前token对应的账号`)
          console.log(`   3. 服务器区域不匹配`)
        }
        
      } else {
        console.log("❌ 获取角色列表失败:", roleListResult?.msg || "未知错误")
        console.log("📊 完整响应:", JSON.stringify(roleListResult, null, 2))
      }
      
    } catch (error) {
      console.log("❌ 获取角色列表异常:", error.message)
    }
    
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message)
  }
}

// 运行测试
testGetRoleList()
  .then(() => {
    console.log("\n✨ 角色列表测试完成！")
    console.log("💡 请检查是否找到了正确的serverId")
  })
  .catch(error => {
    console.error("❌ 测试过程中发生错误:", error)
  })
