import axios from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';

// 测试配置
const TEST_CONFIG = {
    uid: '100381247',
    token: 'eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c',
    proxy: 'http://1907581050:<EMAIL>:9151'
};

// 直接集成到 WutheringWavesAPI 的测试
async function testDirectIntegration() {
    console.log('\n=== 直接集成测试：在 WutheringWavesAPI 中使用 axios ===');
    console.log(`使用 UID: ${TEST_CONFIG.uid}`);
    console.log(`使用代理: ${TEST_CONFIG.proxy}`);
    
    const proxyAgent = new HttpsProxyAgent(TEST_CONFIG.proxy);
    
    // 生成随机字符串
    function generateRandomString(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    // 获取 WutheringWavesUID 风格的请求头
    function getWutheringWavesUIDHeaders(platform = 'ios') {
        const devCode = generateRandomString();
        const headers = {
            'source': platform,
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'devCode': devCode,
            'version': '2.5.0',
            'token': TEST_CONFIG.token
        };

        if (platform === 'ios') {
            headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0';
        }

        return headers;
    }
    
    // 1. 获取 bat token
    console.log('\n1. 获取 bat token...');
    try {
        const headers = getWutheringWavesUIDHeaders('ios');
        
        const data = new URLSearchParams({
            serverId: '76402e5b20be2c39f095a152090afddc',
            roleId: TEST_CONFIG.uid
        });

        const response = await axios.post(
            'https://api.kurobbs.com/aki/roleBox/requestToken',
            data,
            {
                headers,
                httpsAgent: proxyAgent,
                timeout: 10000
            }
        );

        if (response.data.code === 200 || response.data.code === 10902) {
            let accessToken = '';
            const contentData = response.data.data;
            
            if (typeof contentData === 'string') {
                try {
                    const jsonData = JSON.parse(contentData);
                    accessToken = jsonData.accessToken || '';
                } catch (e) {
                    console.log('解析 bat token 失败:', e.message);
                }
            } else if (typeof contentData === 'object') {
                accessToken = contentData.accessToken || '';
            }
            
            if (accessToken) {
                console.log('✅ bat token 获取成功');
                
                // 2. 测试扩展API
                console.log('\n2. 测试扩展API...');
                
                const extendedAPIs = [
                    {
                        name: '探索数据',
                        url: 'https://api.kurobbs.com/aki/roleBox/akiBox/exploreIndex',
                        data: {
                            gameId: 3,
                            serverId: '76402e5b20be2c39f095a152090afddc',
                            roleId: TEST_CONFIG.uid,
                            countryCode: '1'
                        }
                    },
                    {
                        name: '挑战数据',
                        url: 'https://api.kurobbs.com/aki/roleBox/akiBox/challengeDetails',
                        data: {
                            gameId: 3,
                            serverId: '76402e5b20be2c39f095a152090afddc',
                            roleId: TEST_CONFIG.uid
                        }
                    },
                    {
                        name: '数据坞信息',
                        url: 'https://api.kurobbs.com/aki/roleBox/akiBox/calabashData',
                        data: {
                            gameId: 3,
                            serverId: '76402e5b20be2c39f095a152090afddc',
                            roleId: TEST_CONFIG.uid
                        }
                    },
                    {
                        name: '塔楼数据',
                        url: 'https://api.kurobbs.com/aki/roleBox/akiBox/towerIndex',
                        data: {
                            gameId: 3,
                            serverId: '76402e5b20be2c39f095a152090afddc',
                            roleId: TEST_CONFIG.uid
                        }
                    },
                    {
                        name: '更多活动数据',
                        url: 'https://api.kurobbs.com/aki/roleBox/akiBox/moreActivity',
                        data: {
                            gameId: 3,
                            serverId: '76402e5b20be2c39f095a152090afddc',
                            roleId: TEST_CONFIG.uid
                        }
                    }
                ];
                
                let successCount = 0;
                let totalCount = extendedAPIs.length;
                
                for (const apiTest of extendedAPIs) {
                    try {
                        console.log(`\n测试 ${apiTest.name}...`);
                        
                        const apiHeaders = getWutheringWavesUIDHeaders('ios');
                        apiHeaders['b-at'] = accessToken;
                        
                        const apiData = new URLSearchParams(apiTest.data);
                        
                        const apiResponse = await axios.post(apiTest.url, apiData, {
                            headers: apiHeaders,
                            httpsAgent: proxyAgent,
                            timeout: 10000
                        });
                        
                        if (apiResponse.data.code === 200 || apiResponse.data.code === 10902) {
                            console.log(`✅ ${apiTest.name}: 成功`);
                            console.log(`   响应码: ${apiResponse.data.code}`);
                            console.log(`   消息: ${apiResponse.data.msg || 'N/A'}`);
                            successCount++;
                        } else {
                            console.log(`❌ ${apiTest.name}: 失败`);
                            console.log(`   响应码: ${apiResponse.data.code}`);
                            console.log(`   消息: ${apiResponse.data.msg || 'N/A'}`);
                        }
                        
                    } catch (error) {
                        console.log(`❌ ${apiTest.name}: 异常`);
                        if (error.response) {
                            console.log(`   响应码: ${error.response.data.code}`);
                            console.log(`   消息: ${error.response.data.msg || 'N/A'}`);
                        } else {
                            console.log(`   异常信息: ${error.message}`);
                        }
                    }
                }
                
                console.log(`\n=== 直接集成测试结果 ===`);
                console.log(`成功: ${successCount}/${totalCount}`);
                console.log(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
                
                if (successCount === totalCount) {
                    console.log('🎉 所有扩展API测试通过！直接集成成功！');
                    console.log('💡 建议：将此实现方式集成到 WutheringWavesAPI 中');
                } else if (successCount > 0) {
                    console.log('⚠️ 部分扩展API测试成功，需要进一步优化');
                } else {
                    console.log('❌ 所有扩展API测试失败，需要分析问题');
                }
                
            } else {
                console.log('❌ bat token 解析失败');
            }
        } else {
            console.log('❌ bat token 获取失败:', response.data.msg);
        }
        
    } catch (error) {
        console.log('❌ bat token 获取异常:', error.message);
    }
}

// 运行测试
testDirectIntegration().catch(console.error);
