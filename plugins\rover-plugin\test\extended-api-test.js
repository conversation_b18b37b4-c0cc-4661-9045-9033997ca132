/**
 * 扩展API测试 - 模拟WutheringWavesUID的请求方式
 * 测试探索数据、挑战数据等扩展API功能
 */

import wutheringWavesAPI, { WutheringWavesAPI } from "../components/WutheringWavesAPI.js"
import Ku<PERSON><PERSON><PERSON> from "../components/api/kuroapi.js"

// 测试配置
const TEST_CONFIG = {
  // 使用真实的token和UID
  token:
    "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  uid: "100381247",
  serverId: "76402e5b20be2c39f095a152090afddc",
}

// 创建API实例
const api = wutheringWavesAPI // 使用默认导出的实例
const kuroApi = new <PERSON><PERSON><PERSON><PERSON>()

/**
 * 测试方法1: 使用WutheringWavesAPI的方式
 */
async function testWithWutheringWavesAPI() {
  console.log("\n🔬 方法1: 使用WutheringWavesAPI")

  const results = []

  // 先获取bat token
  const batResult = await api.getRequestToken(TEST_CONFIG.uid, TEST_CONFIG.token, "")
  if (!batResult.success) {
    console.error("❌ 无法获取bat token")
    return results
  }

  const batToken = batResult.token
  console.log(`✅ bat token: ${batToken.substring(0, 20)}...`)

  // 测试扩展API
  const extendedAPIs = [
    { name: "探索数据", method: "getExploreData", params: { countryCode: "1" } },
    { name: "挑战数据", method: "getChallengeData", params: {} },
    { name: "数据坞信息", method: "getCalabashData", params: {} },
    { name: "塔楼数据", method: "getTowerData", params: {} },
    { name: "更多活动数据", method: "getMoreActivityData", params: {} },
  ]

  for (const apiTest of extendedAPIs) {
    console.log(`\n📡 测试: ${apiTest.name}`)

    try {
      const result = await api[apiTest.method](
        TEST_CONFIG.uid,
        TEST_CONFIG.token,
        TEST_CONFIG.serverId,
        batToken,
      )

      if (result.success) {
        console.log(`✅ ${apiTest.name} - 成功`)
        results.push({
          api: apiTest.name,
          method: "WutheringWavesAPI",
          success: true,
          message: "请求成功",
        })
      } else {
        console.log(`❌ ${apiTest.name} - 失败: ${result.message}`)
        results.push({
          api: apiTest.name,
          method: "WutheringWavesAPI",
          success: false,
          message: result.message,
        })
      }
    } catch (error) {
      console.error(`💥 ${apiTest.name} - 异常:`, error.message)
      results.push({
        api: apiTest.name,
        method: "WutheringWavesAPI",
        success: false,
        message: `异常: ${error.message}`,
      })
    }

    await new Promise(resolve => setTimeout(resolve, 1500))
  }

  return results
}

/**
 * 测试方法2: 直接使用KuroApi的方式
 */
async function testWithKuroApi() {
  console.log("\n🔬 方法2: 直接使用KuroApi")

  const results = []

  // 先获取bat token
  const batToken = await kuroApi.getRequestToken(TEST_CONFIG.uid, TEST_CONFIG.token, "")
  if (!batToken) {
    console.error("❌ 无法获取bat token")
    return results
  }

  console.log(`✅ bat token: ${batToken.substring(0, 20)}...`)

  // 测试扩展API
  const extendedAPIs = [
    {
      name: "探索数据",
      method: "getExploreData",
      params: [TEST_CONFIG.uid, batToken, "1", TEST_CONFIG.token],
    },
    {
      name: "挑战数据",
      method: "getChallengeData",
      params: [TEST_CONFIG.uid, batToken, TEST_CONFIG.token],
    },
    {
      name: "数据坞信息",
      method: "getCalabashData",
      params: [TEST_CONFIG.uid, batToken, TEST_CONFIG.token],
    },
    {
      name: "塔楼数据",
      method: "getTowerData",
      params: [TEST_CONFIG.uid, batToken, TEST_CONFIG.token],
    },
    {
      name: "更多活动数据",
      method: "getMoreActivityData",
      params: [TEST_CONFIG.uid, batToken, TEST_CONFIG.token],
    },
  ]

  for (const apiTest of extendedAPIs) {
    console.log(`\n📡 测试: ${apiTest.name}`)

    try {
      const result = await kuroApi[apiTest.method](...apiTest.params)

      if (result && (result.code === 200 || result.code === 10902)) {
        console.log(`✅ ${apiTest.name} - 成功 (code: ${result.code})`)
        results.push({
          api: apiTest.name,
          method: "KuroApi",
          success: true,
          message: `成功 (code: ${result.code})`,
        })
      } else {
        console.log(`❌ ${apiTest.name} - 失败: ${result?.msg || "未知错误"}`)
        results.push({
          api: apiTest.name,
          method: "KuroApi",
          success: false,
          message: result?.msg || "未知错误",
        })
      }
    } catch (error) {
      console.error(`💥 ${apiTest.name} - 异常:`, error.message)
      results.push({
        api: apiTest.name,
        method: "KuroApi",
        success: false,
        message: `异常: ${error.message}`,
      })
    }

    await new Promise(resolve => setTimeout(resolve, 1500))
  }

  return results
}

/**
 * 测试方法3: 模拟WutheringWavesUID的原始请求方式
 */
async function testWithRawRequests() {
  console.log("\n🔬 方法3: 模拟WutheringWavesUID原始请求")

  const results = []

  // 先获取bat token
  const batToken = await kuroApi.getRequestToken(TEST_CONFIG.uid, TEST_CONFIG.token, "")
  if (!batToken) {
    console.error("❌ 无法获取bat token")
    return results
  }

  console.log(`✅ bat token: ${batToken.substring(0, 20)}...`)

  // 模拟WutheringWavesUID的请求头和参数
  const baseHeaders = {
    source: "ios",
    "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
    "User-Agent":
      "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
    version: "2.5.0",
    "b-at": batToken,
    token: TEST_CONFIG.token,
  }

  const baseData = {
    gameId: "3",
    serverId: TEST_CONFIG.serverId,
    roleId: TEST_CONFIG.uid,
    channelId: "19",
    countryCode: "1",
  }

  // 测试API端点
  const apiEndpoints = [
    { name: "探索数据", path: "/aki/roleBox/akiBox/exploreIndex" },
    { name: "挑战数据", path: "/aki/roleBox/akiBox/challengeDetails" },
    { name: "数据坞信息", path: "/aki/roleBox/akiBox/calabashData" },
    { name: "塔楼数据", path: "/aki/roleBox/akiBox/towerIndex" },
    { name: "更多活动数据", path: "/aki/roleBox/akiBox/moreActivity" },
  ]

  for (const endpoint of apiEndpoints) {
    console.log(`\n📡 测试: ${endpoint.name}`)

    try {
      // 使用fetch直接请求
      const response = await fetch(`https://api.kurobbs.com${endpoint.path}`, {
        method: "POST",
        headers: baseHeaders,
        body: new URLSearchParams(baseData),
      })

      const result = await response.json()

      if (result && (result.code === 200 || result.code === 10902)) {
        console.log(`✅ ${endpoint.name} - 成功 (code: ${result.code})`)
        results.push({
          api: endpoint.name,
          method: "RawRequest",
          success: true,
          message: `成功 (code: ${result.code})`,
        })
      } else {
        console.log(`❌ ${endpoint.name} - 失败: ${result?.msg || "未知错误"}`)
        results.push({
          api: endpoint.name,
          method: "RawRequest",
          success: false,
          message: result?.msg || "未知错误",
        })
      }
    } catch (error) {
      console.error(`💥 ${endpoint.name} - 异常:`, error.message)
      results.push({
        api: endpoint.name,
        method: "RawRequest",
        success: false,
        message: `异常: ${error.message}`,
      })
    }

    await new Promise(resolve => setTimeout(resolve, 1500))
  }

  return results
}

/**
 * 主测试函数
 */
async function testExtendedAPIs() {
  console.log("🚀 开始扩展API测试 - 对比不同实现方式")
  console.log("=" * 60)

  // 测试三种方法
  const results1 = await testWithWutheringWavesAPI()
  const results2 = await testWithKuroApi()
  const results3 = await testWithRawRequests()

  const allResults = [...results1, ...results2, ...results3]

  // 输出对比结果
  console.log("\n📊 测试结果对比")
  console.log("=" * 60)

  const method1Success = results1.filter(r => r.success).length
  const method2Success = results2.filter(r => r.success).length
  const method3Success = results3.filter(r => r.success).length

  console.log(`🔬 方法1 (WutheringWavesAPI): ${method1Success}/${results1.length} 成功`)
  console.log(`🔬 方法2 (KuroApi): ${method2Success}/${results2.length} 成功`)
  console.log(`🔬 方法3 (原始请求): ${method3Success}/${results3.length} 成功`)

  // 详细结果
  console.log("\n📋 详细结果:")
  const apiNames = ["探索数据", "挑战数据", "数据坞信息", "塔楼数据", "更多活动数据"]

  for (const apiName of apiNames) {
    const result1 = results1.find(r => r.api === apiName)
    const result2 = results2.find(r => r.api === apiName)
    const result3 = results3.find(r => r.api === apiName)

    console.log(`\n${apiName}:`)
    console.log(`  方法1: ${result1?.success ? "✅" : "❌"} ${result1?.message || "未测试"}`)
    console.log(`  方法2: ${result2?.success ? "✅" : "❌"} ${result2?.message || "未测试"}`)
    console.log(`  方法3: ${result3?.success ? "✅" : "❌"} ${result3?.message || "未测试"}`)
  }

  // 保存测试报告
  const report = {
    timestamp: new Date().toISOString(),
    config: TEST_CONFIG,
    results: allResults,
    summary: {
      method1: { total: results1.length, success: method1Success },
      method2: { total: results2.length, success: method2Success },
      method3: { total: results3.length, success: method3Success },
    },
  }

  const fs = await import("fs/promises")
  const reportPath = `test/reports/extended-api-comparison-${Date.now()}.json`
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
  console.log(`\n📄 对比报告已保存: ${reportPath}`)

  return report
}

// 运行测试
if (import.meta.url.endsWith(process.argv[1].replace(/\\/g, "/"))) {
  testExtendedAPIs().catch(console.error)
}

export { testExtendedAPIs }
