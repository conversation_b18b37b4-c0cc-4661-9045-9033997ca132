import wutheringWavesAPI from "../components/WutheringWavesAPI.js"

// 测试配置 - 使用用户提供的最新token
const realToken =
  "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY3NjA3MzMyLCJ1c2VySWQiOjEwODE2NzMyfQ.JBD_vTlMHMWgrptBvTOI05uUYFIdYITsfK9x1bTVHcw,6F02FE7B671ACA64694F19FB67EBEBAD07659846"
const realUid = "100381247"

console.log("🚀 开始测试完整的鸣潮API功能...")
console.log(`📋 使用Token: ${realToken}`)
console.log(`🎮 使用UID: ${realUid}`)
console.log("=".repeat(80))

// 核心API测试
const coreAPIs = [
  { name: "getBaseInfo", method: () => wutheringWavesAPI.getBaseInfo(realUid, realToken) },
  { name: "getRoleInfo", method: () => wutheringWavesAPI.getRoleInfo(realUid, realToken) },
  {
    name: "getRoleDetailInfo",
    method: () => wutheringWavesAPI.getRoleDetailInfo(realUid, realToken),
  },
  { name: "refreshData", method: () => wutheringWavesAPI.refreshData(realUid, realToken) },
  {
    name: "getRealtimeBaseInfo",
    method: () => wutheringWavesAPI.getRealtimeBaseInfo(realUid, realToken),
  },
]

// 扩展API测试（原有的）
const extendedAPIs = [
  { name: "getExploreData", method: () => wutheringWavesAPI.getExploreData(realUid, realToken) },
  {
    name: "getChallengeData",
    method: () => wutheringWavesAPI.getChallengeData(realUid, realToken),
  },
  { name: "getCalabashData", method: () => wutheringWavesAPI.getCalabashData(realUid, realToken) },
  { name: "getTowerData", method: () => wutheringWavesAPI.getTowerData(realUid, realToken) },
  {
    name: "getMoreActivityData",
    method: () => wutheringWavesAPI.getMoreActivityData(realUid, realToken),
  },
]

// 新增API测试（深渊和冥海相关）
const newAPIs = [
  { name: "getAbyssIndex", method: () => wutheringWavesAPI.getAbyssIndex(realUid, realToken) },
  { name: "getAbyssData", method: () => wutheringWavesAPI.getAbyssData(realUid, realToken) },
  { name: "getSlashIndex", method: () => wutheringWavesAPI.getSlashIndex(realUid, realToken) },
  { name: "getSlashDetail", method: () => wutheringWavesAPI.getSlashDetail(realUid, realToken) },
]

// 测试函数
async function testAPI(apiName, apiMethod, category) {
  try {
    console.log(`🔄 测试 ${category} - ${apiName}...`)
    const startTime = Date.now()
    const result = await apiMethod()
    const endTime = Date.now()
    const duration = endTime - startTime

    if (result && result.success) {
      console.log(`✅ ${apiName} 成功 (${duration}ms)`)
      if (result.data) {
        console.log(`   📊 数据大小: ${JSON.stringify(result.data).length} 字符`)
        // 显示一些关键信息
        if (result.data.roleList) {
          console.log(`   👥 角色数量: ${result.data.roleList.length}`)
        }
        if (result.data.exploreList) {
          console.log(`   🗺️ 探索区域: ${result.data.exploreList.length}`)
        }
        if (result.data.challengeInfo) {
          console.log(`   ⚔️ 挑战信息: 已获取`)
        }
        if (result.data.towerAreaList) {
          console.log(`   🏗️ 塔楼区域: ${result.data.towerAreaList.length}`)
        }
        if (result.data.slashAreaList) {
          console.log(`   ⚡ 冥海区域: ${result.data.slashAreaList.length}`)
        }
      }
      return { success: true, duration }
    } else {
      console.log(`❌ ${apiName} 失败: ${result?.message || "未知错误"}`)
      return { success: false, error: result?.message || "未知错误" }
    }
  } catch (error) {
    console.log(`💥 ${apiName} 异常: ${error.message}`)
    return { success: false, error: error.message }
  }
}

// 主测试函数
async function runCompleteTest() {
  const results = {
    core: { total: 0, success: 0, failed: 0, errors: [] },
    extended: { total: 0, success: 0, failed: 0, errors: [] },
    new: { total: 0, success: 0, failed: 0, errors: [] },
  }

  console.log("\n📋 第一部分：核心API测试")
  console.log("=".repeat(50))

  for (const api of coreAPIs) {
    results.core.total++
    const result = await testAPI(api.name, api.method, "核心API")
    if (result.success) {
      results.core.success++
    } else {
      results.core.failed++
      results.core.errors.push(`${api.name}: ${result.error}`)
    }
    console.log("")
  }

  console.log("\n📋 第二部分：扩展API测试")
  console.log("=".repeat(50))

  for (const api of extendedAPIs) {
    results.extended.total++
    const result = await testAPI(api.name, api.method, "扩展API")
    if (result.success) {
      results.extended.success++
    } else {
      results.extended.failed++
      results.extended.errors.push(`${api.name}: ${result.error}`)
    }
    console.log("")
  }

  console.log("\n📋 第三部分：新增API测试（深渊和冥海）")
  console.log("=".repeat(50))

  for (const api of newAPIs) {
    results.new.total++
    const result = await testAPI(api.name, api.method, "新增API")
    if (result.success) {
      results.new.success++
    } else {
      results.new.failed++
      results.new.errors.push(`${api.name}: ${result.error}`)
    }
    console.log("")
  }

  // 输出测试总结
  console.log("\n" + "=".repeat(80))
  console.log("📊 测试总结报告")
  console.log("=".repeat(80))

  console.log(
    `🔵 核心API: ${results.core.success}/${results.core.total} 成功 (${((results.core.success / results.core.total) * 100).toFixed(1)}%)`,
  )
  console.log(
    `🟡 扩展API: ${results.extended.success}/${results.extended.total} 成功 (${((results.extended.success / results.extended.total) * 100).toFixed(1)}%)`,
  )
  console.log(
    `🟢 新增API: ${results.new.success}/${results.new.total} 成功 (${((results.new.success / results.new.total) * 100).toFixed(1)}%)`,
  )

  const totalSuccess = results.core.success + results.extended.success + results.new.success
  const totalTests = results.core.total + results.extended.total + results.new.total
  console.log(
    `🎯 总体成功率: ${totalSuccess}/${totalTests} (${((totalSuccess / totalTests) * 100).toFixed(1)}%)`,
  )

  // 显示错误详情
  const allErrors = [...results.core.errors, ...results.extended.errors, ...results.new.errors]
  if (allErrors.length > 0) {
    console.log("\n❌ 失败的API详情:")
    allErrors.forEach(error => console.log(`   • ${error}`))
  } else {
    console.log("\n🎉 所有API测试都成功了！")
  }

  console.log("\n✨ 测试完成！")
}

// 运行测试
runCompleteTest().catch(console.error)
