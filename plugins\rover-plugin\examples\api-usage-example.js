/**
 * Rover Plugin API 使用示例
 * 展示如何在外部插件中使用 rover-plugin 的各种功能
 */

import roverPlugin from "../lib/index.js"

/**
 * 示例1: 检查用户绑定状态
 */
async function checkUserBindingExample() {
  console.log("=== 示例1: 检查用户绑定状态 ===")
  
  const userId = "123456789"
  const botId = "test_bot"
  
  try {
    const result = await roverPlugin.checkUserBinding(userId, botId)
    
    if (result.success) {
      console.log(`✅ 用户已绑定UID: ${result.uid}`)
      return result.uid
    } else {
      console.log(`❌ 用户未绑定: ${result.message}`)
      return null
    }
  } catch (error) {
    console.error("检查绑定状态失败:", error)
    return null
  }
}

/**
 * 示例2: 获取用户基础信息
 */
async function getUserBaseInfoExample() {
  console.log("\n=== 示例2: 获取用户基础信息 ===")
  
  const userId = "123456789"
  const botId = "test_bot"
  
  try {
    const result = await roverPlugin.callAPI("getBaseInfo", userId, botId)
    
    if (result.success) {
      console.log("✅ 基础信息获取成功")
      console.log("数据预览:", JSON.stringify(result.data, null, 2).substring(0, 200) + "...")
      return result.data
    } else {
      console.log(`❌ 获取基础信息失败: ${result.message}`)
      return null
    }
  } catch (error) {
    console.error("获取基础信息异常:", error)
    return null
  }
}

/**
 * 示例3: 获取角色信息
 */
async function getUserRoleInfoExample() {
  console.log("\n=== 示例3: 获取角色信息 ===")
  
  const userId = "123456789"
  const botId = "test_bot"
  
  try {
    const result = await roverPlugin.callAPI("getRoleInfo", userId, botId)
    
    if (result.success) {
      console.log("✅ 角色信息获取成功")
      
      // 解析角色数据
      let roleData = result.data
      if (typeof roleData === 'string') {
        roleData = JSON.parse(roleData)
      }
      
      if (roleData.roleList && Array.isArray(roleData.roleList)) {
        console.log(`📊 角色数量: ${roleData.roleList.length}`)
        roleData.roleList.forEach((role, index) => {
          console.log(`  ${index + 1}. ${role.roleName} (ID: ${role.roleId})`)
        })
      }
      
      return roleData
    } else {
      console.log(`❌ 获取角色信息失败: ${result.message}`)
      return null
    }
  } catch (error) {
    console.error("获取角色信息异常:", error)
    return null
  }
}

/**
 * 示例4: 获取角色详细信息
 */
async function getRoleDetailExample() {
  console.log("\n=== 示例4: 获取角色详细信息 ===")
  
  const userId = "123456789"
  const botId = "test_bot"
  const charId = "1403" // 鸣潮角色ID
  
  try {
    const result = await roverPlugin.callAPI("getRoleDetailInfo", userId, botId, { charId })
    
    if (result.success) {
      console.log("✅ 角色详细信息获取成功")
      console.log("数据预览:", JSON.stringify(result.data, null, 2).substring(0, 200) + "...")
      return result.data
    } else {
      console.log(`❌ 获取角色详细信息失败: ${result.message}`)
      return null
    }
  } catch (error) {
    console.error("获取角色详细信息异常:", error)
    return null
  }
}

/**
 * 示例5: 刷新用户数据
 */
async function refreshUserDataExample() {
  console.log("\n=== 示例5: 刷新用户数据 ===")
  
  const userId = "123456789"
  const botId = "test_bot"
  
  try {
    const result = await roverPlugin.callAPI("refreshData", userId, botId)
    
    if (result.success) {
      console.log("✅ 数据刷新成功")
      return true
    } else {
      console.log(`❌ 数据刷新失败: ${result.message}`)
      return false
    }
  } catch (error) {
    console.error("数据刷新异常:", error)
    return false
  }
}

/**
 * 示例6: 获取实时基础信息（不使用缓存）
 */
async function getRealtimeInfoExample() {
  console.log("\n=== 示例6: 获取实时基础信息 ===")
  
  const userId = "123456789"
  const botId = "test_bot"
  
  try {
    const result = await roverPlugin.callAPI("getRealtimeBaseInfo", userId, botId)
    
    if (result.success) {
      console.log("✅ 实时基础信息获取成功")
      console.log("数据预览:", JSON.stringify(result.data, null, 2).substring(0, 200) + "...")
      return result.data
    } else {
      console.log(`❌ 获取实时基础信息失败: ${result.message}`)
      return null
    }
  } catch (error) {
    console.error("获取实时基础信息异常:", error)
    return null
  }
}

/**
 * 示例7: 获取支持的API方法
 */
async function getSupportedMethodsExample() {
  console.log("\n=== 示例7: 获取支持的API方法 ===")
  
  try {
    const methods = roverPlugin.getSupportedMethods()
    console.log("✅ 支持的API方法:")
    methods.forEach((method, index) => {
      console.log(`  ${index + 1}. ${method}`)
    })
    return methods
  } catch (error) {
    console.error("获取支持方法失败:", error)
    return []
  }
}

/**
 * 示例8: 获取插件状态
 */
async function getPluginStatusExample() {
  console.log("\n=== 示例8: 获取插件状态 ===")
  
  try {
    const status = roverPlugin.getStatus()
    console.log("✅ 插件状态:")
    console.log(JSON.stringify(status, null, 2))
    return status
  } catch (error) {
    console.error("获取插件状态失败:", error)
    return null
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log("🚀 Rover Plugin API 使用示例")
  console.log("=" * 50)
  
  try {
    // 1. 检查用户绑定状态
    const uid = await checkUserBindingExample()
    
    if (!uid) {
      console.log("\n⚠️ 用户未绑定，跳过需要绑定的API示例")
      console.log("请先使用 rover-plugin 的绑定功能绑定鸣潮账号")
    } else {
      // 2. 获取基础信息
      await getUserBaseInfoExample()
      
      // 3. 获取角色信息
      await getUserRoleInfoExample()
      
      // 4. 获取角色详细信息
      await getRoleDetailExample()
      
      // 5. 刷新数据
      await refreshUserDataExample()
      
      // 6. 获取实时信息
      await getRealtimeInfoExample()
    }
    
    // 7. 获取支持的方法（不需要绑定）
    await getSupportedMethodsExample()
    
    // 8. 获取插件状态（不需要绑定）
    await getPluginStatusExample()
    
    console.log("\n🎉 所有示例运行完成！")
    
  } catch (error) {
    console.error("运行示例时发生错误:", error)
  }
}

// 如果直接运行此文件，则执行所有示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples()
}

// 导出示例函数供其他模块使用
export {
  checkUserBindingExample,
  getUserBaseInfoExample,
  getUserRoleInfoExample,
  getRoleDetailExample,
  refreshUserDataExample,
  getRealtimeInfoExample,
  getSupportedMethodsExample,
  getPluginStatusExample,
  runAllExamples
}
