/**
 * WutheringWavesUID 实现方式分析和优化测试
 * 基于 https://github.com/tyql688/WutheringWavesUID 的实现方式
 */

import wutheringWavesAPI from "../components/WutheringWavesAPI.js"
import KuroA<PERSON> from "../components/api/kuroapi.js"

// 测试配置
const TEST_CONFIG = {
  token: "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  uid: "100381247",
  serverId: "76402e5b20be2c39f095a152090afddc",
}

/**
 * 分析当前实现与 WutheringWavesUID 的差异
 */
async function analyzeImplementationDifferences() {
  console.log("🔍 分析当前实现与 WutheringWavesUID 的差异")
  console.log("=" * 60)
  
  // 1. 检查代理配置
  console.log("\n📡 1. 代理配置分析")
  const proxyUrl = wutheringWavesAPI.getAvailableProxy()
  console.log(`当前代理: ${proxyUrl}`)
  
  // 2. 检查请求头生成
  console.log("\n📋 2. 请求头分析")
  const headers = await wutheringWavesAPI.getHeaders(TEST_CONFIG.token, null, TEST_CONFIG.uid)
  console.log("当前请求头:")
  console.log(JSON.stringify(headers, null, 2))
  
  // 3. 检查bat token获取
  console.log("\n🔑 3. bat token获取分析")
  const batResult = await wutheringWavesAPI.getRequestToken(TEST_CONFIG.uid, TEST_CONFIG.token, "")
  if (batResult.success) {
    console.log(`✅ bat token获取成功: ${batResult.token.substring(0, 20)}...`)
  } else {
    console.log(`❌ bat token获取失败: ${batResult.message}`)
    return
  }
  
  // 4. 分析请求参数
  console.log("\n📝 4. 请求参数分析")
  const baseParams = {
    gameId: "3",
    serverId: TEST_CONFIG.serverId,
    roleId: TEST_CONFIG.uid,
    channelId: "19",
    countryCode: "1"
  }
  console.log("基础参数:")
  console.log(JSON.stringify(baseParams, null, 2))
  
  return batResult.token
}

/**
 * 测试优化的请求方式 - 模拟 WutheringWavesUID 的精确实现
 */
async function testOptimizedRequests(batToken) {
  console.log("\n🚀 测试优化的请求方式")
  console.log("=" * 60)
  
  const results = []
  
  // 优化的请求头 - 完全按照 WutheringWavesUID 的方式
  const optimizedHeaders = {
    "source": "ios",
    "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
    "version": "2.5.0",
    "b-at": batToken,
    "token": TEST_CONFIG.token,
    // 添加可能缺失的头部
    "Accept": "*/*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site"
  }
  
  // 测试API端点
  const apiEndpoints = [
    { 
      name: "探索数据", 
      path: "/aki/roleBox/akiBox/exploreIndex",
      params: {
        gameId: "3",
        serverId: TEST_CONFIG.serverId,
        roleId: TEST_CONFIG.uid,
        channelId: "19",
        countryCode: "1"
      }
    },
    { 
      name: "挑战数据", 
      path: "/aki/roleBox/akiBox/challengeDetails",
      params: {
        gameId: "3",
        serverId: TEST_CONFIG.serverId,
        roleId: TEST_CONFIG.uid,
        channelId: "19",
        countryCode: "1"
      }
    },
    { 
      name: "数据坞信息", 
      path: "/aki/roleBox/akiBox/calabashData",
      params: {
        gameId: "3",
        serverId: TEST_CONFIG.serverId,
        roleId: TEST_CONFIG.uid,
        channelId: "19",
        countryCode: "1"
      }
    }
  ]
  
  for (const endpoint of apiEndpoints) {
    console.log(`\n📡 测试: ${endpoint.name}`)
    
    try {
      // 方法1: 使用代理的原始请求
      console.log("  🌐 方法1: 使用代理的原始请求")
      const proxyUrl = "http://1907581050:<EMAIL>:9151"
      
      const { HttpsProxyAgent } = await import("https-proxy-agent")
      const agent = new HttpsProxyAgent(proxyUrl)
      
      const response1 = await fetch(`https://api.kurobbs.com${endpoint.path}`, {
        method: "POST",
        headers: optimizedHeaders,
        body: new URLSearchParams(endpoint.params),
        agent: agent
      })
      
      const result1 = await response1.json()
      
      if (result1 && (result1.code === 200 || result1.code === 10902)) {
        console.log(`    ✅ 成功 (code: ${result1.code})`)
        results.push({ api: endpoint.name, method: "代理原始请求", success: true, code: result1.code })
      } else {
        console.log(`    ❌ 失败: ${result1?.msg || "未知错误"} (code: ${result1?.code})`)
        results.push({ api: endpoint.name, method: "代理原始请求", success: false, message: result1?.msg, code: result1?.code })
      }
      
      // 等待一段时间避免请求过快
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // 方法2: 不使用代理的直连请求（对比测试）
      console.log("  🔗 方法2: 直连请求（对比）")
      
      const response2 = await fetch(`https://api.kurobbs.com${endpoint.path}`, {
        method: "POST",
        headers: optimizedHeaders,
        body: new URLSearchParams(endpoint.params)
      })
      
      const result2 = await response2.json()
      
      if (result2 && (result2.code === 200 || result2.code === 10902)) {
        console.log(`    ✅ 成功 (code: ${result2.code})`)
        results.push({ api: endpoint.name, method: "直连请求", success: true, code: result2.code })
      } else {
        console.log(`    ❌ 失败: ${result2?.msg || "未知错误"} (code: ${result2?.code})`)
        results.push({ api: endpoint.name, method: "直连请求", success: false, message: result2?.msg, code: result2?.code })
      }
      
    } catch (error) {
      console.error(`  💥 异常: ${error.message}`)
      results.push({ api: endpoint.name, method: "异常", success: false, message: error.message })
    }
    
    // 每个API之间等待更长时间
    await new Promise(resolve => setTimeout(resolve, 5000))
  }
  
  return results
}

/**
 * 分析结果并提供优化建议
 */
function analyzeResults(results) {
  console.log("\n📊 结果分析")
  console.log("=" * 60)
  
  const proxyResults = results.filter(r => r.method === "代理原始请求")
  const directResults = results.filter(r => r.method === "直连请求")
  
  const proxySuccess = proxyResults.filter(r => r.success).length
  const directSuccess = directResults.filter(r => r.success).length
  
  console.log(`🌐 代理请求成功率: ${proxySuccess}/${proxyResults.length} (${((proxySuccess/proxyResults.length)*100).toFixed(1)}%)`)
  console.log(`🔗 直连请求成功率: ${directSuccess}/${directResults.length} (${((directSuccess/directResults.length)*100).toFixed(1)}%)`)
  
  // 详细分析
  console.log("\n📋 详细分析:")
  const apiNames = ["探索数据", "挑战数据", "数据坞信息"]
  
  for (const apiName of apiNames) {
    const proxyResult = proxyResults.find(r => r.api === apiName)
    const directResult = directResults.find(r => r.api === apiName)
    
    console.log(`\n${apiName}:`)
    console.log(`  代理: ${proxyResult?.success ? "✅" : "❌"} ${proxyResult?.message || proxyResult?.code || ""}`)
    console.log(`  直连: ${directResult?.success ? "✅" : "❌"} ${directResult?.message || directResult?.code || ""}`)
  }
  
  // 提供优化建议
  console.log("\n💡 优化建议:")
  
  if (proxySuccess > directSuccess) {
    console.log("✅ 代理配置有效，建议继续使用代理")
  } else if (directSuccess > proxySuccess) {
    console.log("⚠️ 直连效果更好，可能代理配置需要调整")
  } else {
    console.log("🤔 两种方式效果相似，可能需要其他优化策略")
  }
  
  if (proxySuccess === 0 && directSuccess === 0) {
    console.log("❌ 所有请求都失败，可能的原因:")
    console.log("   1. 请求头格式不正确")
    console.log("   2. bat token无效或过期")
    console.log("   3. 请求参数错误")
    console.log("   4. 服务器端风险检测机制升级")
    console.log("   5. 需要更复杂的反检测策略")
  }
}

/**
 * 主函数
 */
async function main() {
  console.log("🔬 WutheringWavesUID 实现方式分析")
  console.log("基于 https://github.com/tyql688/WutheringWavesUID")
  console.log("=" * 80)
  
  try {
    // 1. 分析当前实现
    const batToken = await analyzeImplementationDifferences()
    
    if (!batToken) {
      console.error("❌ 无法获取bat token，停止测试")
      return
    }
    
    // 2. 测试优化的请求方式
    const results = await testOptimizedRequests(batToken)
    
    // 3. 分析结果
    analyzeResults(results)
    
    // 4. 保存分析报告
    const report = {
      timestamp: new Date().toISOString(),
      config: TEST_CONFIG,
      batToken: batToken.substring(0, 20) + "...",
      results: results,
      analysis: {
        proxySuccess: results.filter(r => r.method === "代理原始请求" && r.success).length,
        directSuccess: results.filter(r => r.method === "直连请求" && r.success).length,
        totalTests: results.length
      }
    }
    
    const fs = await import("fs/promises")
    const reportPath = `test/reports/wutheringwaves-uid-analysis-${Date.now()}.json`
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 分析报告已保存: ${reportPath}`)
    
  } catch (error) {
    console.error("❌ 分析过程中发生错误:", error)
  }
}

// 运行分析
if (import.meta.url.endsWith(process.argv[1].replace(/\\/g, "/"))) {
  main().catch(console.error)
}

export { main as analyzeWutheringWavesUID }
