import <PERSON><PERSON><PERSON><PERSON> from "../components/api/kuroapi.js"

// 测试配置
const TEST_CONFIG = {
  uid: "100381247",
  token:
    "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
}

// 直接测试 KuroApi 的扩展API方法
async function testKuroApiDirect() {
  console.log("\n=== 直接测试 KuroApi 扩展API方法 ===")
  console.log(`使用 UID: ${TEST_CONFIG.uid}`)

  const kuroApi = new KuroApi()

  // 1. 获取 bat token
  console.log("\n1. 获取 bat token...")
  try {
    const accessToken = await kuro<PERSON>pi.getRequestToken(TEST_CONFIG.uid, TEST_CONFIG.token, "")

    if (accessToken && typeof accessToken === "string" && accessToken.length > 0) {
      console.log("✅ bat token 获取成功")
      console.log(`   bat token: ${accessToken.substring(0, 20)}...`)

      // 2. 测试扩展API
      console.log("\n2. 测试扩展API...")

      const extendedAPIs = [
        {
          name: "探索数据",
          method: "getExploreData",
          params: [TEST_CONFIG.uid, accessToken, "1", TEST_CONFIG.token],
        },
        {
          name: "挑战数据",
          method: "getChallengeData",
          params: [TEST_CONFIG.uid, accessToken, TEST_CONFIG.token],
        },
        {
          name: "数据坞信息",
          method: "getCalabashData",
          params: [TEST_CONFIG.uid, accessToken, TEST_CONFIG.token],
        },
        {
          name: "塔楼数据",
          method: "getTowerData",
          params: [TEST_CONFIG.uid, accessToken, TEST_CONFIG.token],
        },
        {
          name: "更多活动数据",
          method: "getMoreActivityData",
          params: [TEST_CONFIG.uid, accessToken, TEST_CONFIG.token],
        },
      ]

      let successCount = 0
      let totalCount = extendedAPIs.length

      for (const apiTest of extendedAPIs) {
        try {
          console.log(`\n测试 ${apiTest.name}...`)
          console.log(
            `   调用: kuroApi.${apiTest.method}(${apiTest.params.map(p => (typeof p === "string" && p.length > 20 ? p.substring(0, 20) + "..." : p)).join(", ")})`,
          )

          const result = await kuroApi[apiTest.method](...apiTest.params)

          if (result && (result.code === 200 || result.code === 10902)) {
            console.log(`✅ ${apiTest.name}: 成功`)
            console.log(`   响应码: ${result.code}`)
            console.log(`   消息: ${result.msg || "N/A"}`)
            successCount++
          } else {
            console.log(`❌ ${apiTest.name}: 失败`)
            console.log(`   响应码: ${result?.code || "N/A"}`)
            console.log(`   消息: ${result?.msg || "N/A"}`)
            console.log(`   完整响应:`, JSON.stringify(result, null, 2))
          }
        } catch (error) {
          console.log(`❌ ${apiTest.name}: 异常`)
          console.log(`   异常信息: ${error.message}`)
          console.log(`   异常堆栈:`, error.stack)
        }
      }

      console.log(`\n=== KuroApi 直接测试结果 ===`)
      console.log(`成功: ${successCount}/${totalCount}`)
      console.log(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`)

      if (successCount === totalCount) {
        console.log("🎉 所有扩展API测试通过！KuroApi 修复成功！")
      } else if (successCount > 0) {
        console.log("⚠️ 部分扩展API测试成功，需要进一步优化")
      } else {
        console.log("❌ 所有扩展API测试失败，需要检查 KuroApi 实现")
      }
    } else {
      console.log("❌ bat token 获取失败")
      console.log(`   返回值: ${accessToken}`)
      console.log(`   类型: ${typeof accessToken}`)
    }
  } catch (error) {
    console.log("❌ bat token 获取异常:", error.message)
    console.log("   异常堆栈:", error.stack)
  }
}

// 运行测试
testKuroApiDirect().catch(console.error)
