import config from "./config.js"
import refreshPanelManager from "./RefreshPanelManager.js"
import { WavesUser } from "./db/database.js"
import KuroApi from "./api/kuroapi.js"

// 鸣潮API类
class WutheringWavesAPI {
  constructor() {
    this.GAME_ID = 3
    this.SERVER_ID = "76402e5b20be2c39f095a152090afddc"
    this.KURO_VERSION = "2.5.0"

    // 初始化kuroapi
    this.kuroapi = new KuroApi()

    // bat token缓存 - 参考WutheringWavesUID的batMap (永久缓存，bat token是固定的)
    this.batTokenCache = new Map()

    // 角色数据缓存 (永久缓存，只有用户主动刷新面板时才更新)
    this.roleDataCache = new Map()

    // 实时数据不缓存：体力、成就数量、活跃天数等

    // 代理轮换状态
    this.failedProxies = new Set() // 记录失效的代理
    this.currentProxyIndex = 0 // 当前使用的代理索引
    this.failedKuroProxies = new Set() // 记录失效的库街区API代理
    this.currentKuroProxyIndex = 0 // 当前使用的库街区API代理索引

    // 代理失效计数和时间管理
    this.proxyFailureCount = new Map() // 记录代理失败次数 {proxyUrl: {count: number, firstFailTime: timestamp}}
    this.kuroProxyFailureCount = new Map() // 记录库街区API代理失败次数

    // 失效检测配置
    this.FAILURE_THRESHOLD = 5 // 失败5次后标记为失效
    this.FAILURE_RESET_TIME = 5 * 60 * 60 * 1000 // 5小时后重置失效状态 (毫秒)
  }

  // 生成随机字符串
  generateRandomString(length = 32) {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
    let result = ""
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // 获取通用请求头 - 按照WutheringWavesUID的方式
  async getCommonHeader(platform = "ios") {
    // get_common_header 总是使用随机字符串作为devCode
    // 只有 get_headers_ios 才使用 IP+User-Agent 格式
    const devCode = this.generateRandomString()
    return {
      source: platform,
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent":
        platform === "ios"
          ? "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
          : "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      devCode: devCode,
      version: this.KURO_VERSION,
    }
  }

  // 获取iOS设备码 -
  async getDevCodeIOS() {
    const { getPublicIp } = await import("../../utils/network.js")
    const ip = await getPublicIp()
    const userAgent =
      "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
    return `${ip}, ${userAgent}`
  }

  // 获取请求头 - 完全按照WutheringWavesUID的实现
  async getHeaders(ck = null, platform = null, queryRoleId = null) {
    if (!ck && !platform) {
      return await this.getCommonHeader("h5")
    }

    let bat = ""
    let did = ""
    let tokenRoleId = ""
    platform = platform || "ios"

    if (ck) {
      // 🔑 关键：完全按照WutheringWavesUID的逻辑
      // 获取当前role信息 - 优先根据queryRoleId查找特定用户
      if (queryRoleId) {
        try {
          const wavesUser = await WavesUser.selectDataByCookieAndUid(ck, queryRoleId)
          if (wavesUser) {
            platform = wavesUser.platform || "ios"
            bat = wavesUser.bat || ""
            did = wavesUser.did || ""
            tokenRoleId = wavesUser.uid || ""
          }
        } catch (error) {
          // 数据库查询失败不影响正常使用，使用默认值
          console.warn("从数据库获取特定用户信息失败，使用默认配置:", error.message)
        }
      }

      // 2次校验 - 如果没有找到特定用户，尝试通过cookie查找任意用户
      if (!tokenRoleId) {
        try {
          const wavesUser = await WavesUser.selectDataByCookie(ck)
          if (wavesUser) {
            platform = wavesUser.platform || "ios"
            bat = wavesUser.bat || ""
            did = wavesUser.did || ""
            tokenRoleId = wavesUser.uid || ""
          }
        } catch (error) {
          // 数据库查询失败不影响正常使用，使用默认值
          console.warn("从数据库获取通用用户信息失败，使用默认配置:", error.message)
        }
      }
    }

    // 获取基础header - 按照WutheringWavesUID的平台选择逻辑
    let header
    if (platform === "ios") {
      header = await this.getCommonHeader("ios")
    } else {
      header = await this.getCommonHeader(platform || "ios")
    }

    // 🔑 关键：按照WutheringWavesUID的方式添加认证信息
    if (bat) {
      header["b-at"] = bat
    }
    if (did) {
      header["did"] = did
    }
    if (tokenRoleId) {
      header["roleId"] = tokenRoleId
    }

    return header
  }

  /**
   * 检查代理是否应该被标记为失效
   * @param {string} proxyUrl 代理URL
   * @param {Map} failureCountMap 失败计数Map
   * @returns {boolean} 是否应该标记为失效
   */
  shouldMarkProxyAsFailed(proxyUrl, failureCountMap) {
    if (!failureCountMap.has(proxyUrl)) {
      // 第一次失败，记录失败信息
      failureCountMap.set(proxyUrl, {
        count: 1,
        firstFailTime: Date.now(),
      })
      console.log(`⚠️ 代理首次失败 [1/${this.FAILURE_THRESHOLD}]: ${proxyUrl}`)
      return false
    }

    const failureInfo = failureCountMap.get(proxyUrl)
    const now = Date.now()

    // 检查是否超过5小时，如果是则重置计数
    if (now - failureInfo.firstFailTime > this.FAILURE_RESET_TIME) {
      failureCountMap.set(proxyUrl, {
        count: 1,
        firstFailTime: now,
      })
      console.log(`🔄 代理失败计数重置 [1/${this.FAILURE_THRESHOLD}]: ${proxyUrl}`)
      return false
    }

    // 增加失败计数
    failureInfo.count++
    console.log(`⚠️ 代理失败计数增加 [${failureInfo.count}/${this.FAILURE_THRESHOLD}]: ${proxyUrl}`)

    // 检查是否达到失效阈值
    if (failureInfo.count >= this.FAILURE_THRESHOLD) {
      console.warn(`❌ 代理达到失效阈值，标记为失效: ${proxyUrl}`)
      return true
    }

    return false
  }

  /**
   * 清理过期的失效代理
   * @param {Set} failedProxiesSet 失效代理Set
   * @param {Map} failureCountMap 失败计数Map
   */
  cleanupExpiredFailedProxies(failedProxiesSet, failureCountMap) {
    const now = Date.now()
    const expiredProxies = []

    for (const proxyUrl of failedProxiesSet) {
      const failureInfo = failureCountMap.get(proxyUrl)
      if (failureInfo && now - failureInfo.firstFailTime > this.FAILURE_RESET_TIME) {
        expiredProxies.push(proxyUrl)
      }
    }

    expiredProxies.forEach(proxyUrl => {
      failedProxiesSet.delete(proxyUrl)
      failureCountMap.delete(proxyUrl)
      console.log(`🔄 代理失效状态已过期，重新启用: ${proxyUrl}`)
    })
  }

  /**
   * 获取可用的代理URL（支持轮换）
   * @returns {string|null} 代理URL
   */
  getAvailableProxy() {
    const allProxies = config.getAllProxies()
    if (allProxies.length === 0) return null

    // 清理过期的失效代理
    this.cleanupExpiredFailedProxies(this.failedProxies, this.proxyFailureCount)

    // 如果没有启用轮换，直接返回第一个代理
    if (!config.isProxyRotationEnabled()) {
      return allProxies[0]
    }

    // 轮询模式：每次使用下一个可用代理
    for (let i = 0; i < allProxies.length; i++) {
      const proxyIndex = (this.currentProxyIndex + i) % allProxies.length
      const proxy = allProxies[proxyIndex]

      if (!this.failedProxies.has(proxy)) {
        // 轮询到下一个代理
        this.currentProxyIndex = (proxyIndex + 1) % allProxies.length
        if (allProxies.length > 1) {
          console.log(`🔄 轮询使用代理 [${proxyIndex + 1}/${allProxies.length}]: ${proxy}`)
        }
        return proxy
      }
    }

    // 如果所有代理都失效，清除失效记录并重新开始
    console.warn("🔄 所有代理都已失效，重置代理状态")
    this.failedProxies.clear()
    this.proxyFailureCount.clear()
    this.currentProxyIndex = 0
    return allProxies[0]
  }

  /**
   * 获取可用的库街区API代理URL（支持轮换）
   * @returns {string} 库街区API代理URL
   */
  getAvailableKuroProxy() {
    const allProxies = config.getAllKuroUrlProxies()
    if (allProxies.length === 0) return "https://api.kurobbs.com"

    // 清理过期的失效代理
    this.cleanupExpiredFailedProxies(this.failedKuroProxies, this.kuroProxyFailureCount)

    // 如果没有启用库街区API代理轮换，直接返回第一个代理
    if (!config.isKuroProxyRotationEnabled()) {
      return allProxies[0]
    }

    // 轮询模式：每次使用下一个可用代理
    for (let i = 0; i < allProxies.length; i++) {
      const proxyIndex = (this.currentKuroProxyIndex + i) % allProxies.length
      const proxy = allProxies[proxyIndex]

      if (!this.failedKuroProxies.has(proxy)) {
        // 轮询到下一个代理
        this.currentKuroProxyIndex = (proxyIndex + 1) % allProxies.length
        if (allProxies.length > 1) {
          console.log(`🔄 轮询使用库街区API代理 [${proxyIndex + 1}/${allProxies.length}]: ${proxy}`)
        }
        return proxy
      }
    }

    // 如果所有代理都失效，清除失效记录并重新开始
    console.warn("🔄 所有库街区API代理都已失效，重置代理状态")
    this.failedKuroProxies.clear()
    this.kuroProxyFailureCount.clear()
    this.currentKuroProxyIndex = 0
    return allProxies[0]
  }

  /**
   * 标记代理为失效（智能检测）
   * @param {string} proxyUrl 失效的代理URL
   */
  markProxyAsFailed(proxyUrl) {
    if (proxyUrl) {
      // 使用智能失效检测
      const shouldMarkFailed = this.shouldMarkProxyAsFailed(proxyUrl, this.proxyFailureCount)

      if (shouldMarkFailed) {
        this.failedProxies.add(proxyUrl)
        console.warn(`❌ 代理已标记为失效: ${proxyUrl}`)

        // 切换到下一个代理
        const allProxies = config.getAllProxies()
        if (allProxies.length > 1) {
          this.currentProxyIndex = (this.currentProxyIndex + 1) % allProxies.length
          const nextProxy = this.getAvailableProxy()
          if (nextProxy) {
            console.log(`🔄 切换到代理: ${nextProxy}`)
          }
        }
      }
    }
  }

  /**
   * 标记库街区API代理为失效（智能检测）
   * @param {string} proxyUrl 失效的库街区API代理URL
   */
  markKuroProxyAsFailed(proxyUrl) {
    if (proxyUrl && proxyUrl !== "https://api.kurobbs.com") {
      // 使用智能失效检测
      const shouldMarkFailed = this.shouldMarkProxyAsFailed(proxyUrl, this.kuroProxyFailureCount)

      if (shouldMarkFailed) {
        this.failedKuroProxies.add(proxyUrl)
        console.warn(`❌ 库街区API代理已标记为失效: ${proxyUrl}`)

        // 切换到下一个代理
        const allProxies = config.getAllKuroUrlProxies()
        if (allProxies.length > 1) {
          this.currentKuroProxyIndex = (this.currentKuroProxyIndex + 1) % allProxies.length
          const nextProxy = this.getAvailableKuroProxy()
          if (nextProxy) {
            console.log(`🔄 切换到库街区API代理: ${nextProxy}`)
          }
        }
      }
    }
  }

  // 发送请求 - 支持代理和缓存
  async wavesRequest(
    url,
    method = "POST",
    header = null,
    data = null,
    cacheKey = null,
    funcName = "unknown",
  ) {
    if (!header) {
      header = await this.getHeaders()
    }

    // 移除roleId字段，避免干扰
    if (header.roleId) {
      delete header.roleId
    }

    // 检查永久缓存 (角色数据永不过期，只有用户主动刷新时才更新)
    if (cacheKey && this.roleDataCache.has(cacheKey)) {
      const cached = this.roleDataCache.get(cacheKey)
      console.log(`📦 使用永久缓存数据: ${cacheKey}`)
      return cached.data
    }

    // 按照WutheringWavesUID的方式检查是否需要代理
    const proxyFuncs = config.getNeedProxyFunc() || []
    const needProxy = proxyFuncs.includes("all") || proxyFuncs.includes(funcName)
    const proxyUrl = needProxy ? this.getAvailableProxy() : null

    console.log(`📡 请求: ${method} ${url}${needProxy ? " (代理)" : ""}`)
    // 只在调试模式下显示详细代理信息
    if (process.env.NODE_ENV === "development") {
      console.log(`🔍 函数名: ${funcName}, 需要代理: ${needProxy}`)
      if (proxyUrl) {
        console.log(`🌐 使用代理: ${proxyUrl}`)
      }
    }

    try {
      let result

      if (proxyUrl) {
        // 使用代理
        const axios = (await import("axios")).default
        const { HttpsProxyAgent } = await import("https-proxy-agent")
        const { SocksProxyAgent } = await import("socks-proxy-agent")

        // 根据代理URL类型选择代理agent
        let agent
        if (proxyUrl.startsWith("socks")) {
          agent = new SocksProxyAgent(proxyUrl)
        } else {
          agent = new HttpsProxyAgent(proxyUrl)
        }

        const requestOptions = {
          method,
          url,
          headers: header,
          httpsAgent: agent,
          httpAgent: agent, // 同时设置http和https agent
          timeout: 30000,
          validateStatus: () => true, // 接受所有状态码
        }

        if (data) {
          if (method === "POST") {
            requestOptions.data = new URLSearchParams(data).toString()
          } else {
            requestOptions.params = data
          }
        }

        const response = await axios(requestOptions)
        result = response.data
      } else {
        // 不使用代理 - 使用原生 fetch
        const options = {
          method,
          headers: header,
        }

        if (data) {
          if (method === "POST") {
            options.body = new URLSearchParams(data)
          } else {
            // GET请求将参数添加到URL
            const params = new URLSearchParams(data)
            url += (url.includes("?") ? "&" : "?") + params.toString()
          }
        }

        const response = await fetch(url, options)
        result = await response.json()
      }

      console.log(`✅ 响应: code=${result.code}, msg=${result.msg}`)

      // 永久缓存成功的响应 (角色数据永不过期)
      // 200和10902都是成功的响应码
      if (cacheKey && (result.code === 200 || result.code === 10902)) {
        this.roleDataCache.set(cacheKey, {
          data: result,
          timestamp: Date.now(), // 仅用于记录缓存时间，不用于过期检查
        })
        console.log(`💾 永久缓存数据: ${cacheKey}`)
      }

      return result
    } catch (error) {
      console.error(`❌ 请求失败:`, error.message)

      // 检查是否需要进行代理轮换重试
      let shouldRetry = false

      // 1. 本地代理失效重试
      if (proxyUrl && config.isProxyRotationEnabled()) {
        const allProxies = config.getAllProxies()
        if (allProxies.length > 1) {
          console.log(`🔄 本地代理请求失败，记录失败次数...`)
          this.markProxyAsFailed(proxyUrl)

          // 检查是否还有可用代理
          const hasAvailableProxy = allProxies.some(proxy => !this.failedProxies.has(proxy))
          if (hasAvailableProxy) {
            shouldRetry = true
          }
        }
      }

      // 2. 库街区API代理失效重试
      if (config.isKuroProxyRotationEnabled()) {
        const allKuroProxies = config.getAllKuroUrlProxies()
        const currentKuroProxy = url.split("/")[2] // 从URL中提取域名
        const kuroProxyUrl = `https://${currentKuroProxy}`

        if (allKuroProxies.length > 1) {
          console.log(`🔄 库街区API代理请求失败，记录失败次数...`)
          this.markKuroProxyAsFailed(kuroProxyUrl)

          // 检查是否还有可用代理
          const hasAvailableProxy = allKuroProxies.some(proxy => !this.failedKuroProxies.has(proxy))
          if (hasAvailableProxy) {
            shouldRetry = true
          }
        }
      }

      // 执行重试（避免无限递归）
      if (shouldRetry && !error.isRetry) {
        const retryError = new Error(error.message)
        retryError.isRetry = true
        try {
          return await this.wavesRequest(url, method, header, data, cacheKey, funcName)
        } catch (retryErr) {
          console.error(`❌ 代理轮换重试也失败:`, retryErr.message)
        }
      }

      return { code: 999, msg: `请求失败: ${error.message}` }
    }
  }

  /**
   * 从URL获取函数名
   */
  getFunctionNameFromUrl(url) {
    if (url.includes("getRoleDetail")) return "get_role_detail_info"
    if (url.includes("baseData")) return "get_base_info"
    if (url.includes("refreshData")) return "refresh_data"
    if (url.includes("requestToken")) return "get_request_token"
    if (url.includes("roleData")) return "get_role_info"
    if (url.includes("role/list")) return "get_kuro_role_list"
    return "unknown"
  }

  /**
   * 获取角色列表
   */
  async getKuroRoleList(token, did) {
    const platform = "ios" // login_platform() 返回 "ios"
    const header = await this.getCommonHeader(platform)

    // 设置请求头
    header.token = token
    header.devCode = did

    const data = {
      gameId: this.GAME_ID,
    }

    const url = `${this.getAvailableKuroProxy()}/gamer/role/list`

    // 不使用缓存，每次都重新请求
    const response = await this.wavesRequest(
      url,
      "POST",
      header,
      data,
      null, // 不使用缓存
      "get_kuro_role_list",
    )

    if ((response.code === 200 || response.code === 10902) && response.data) {
      return { success: true, platform, data: response.data }
    } else {
      return { success: false, message: response.msg || "获取角色列表失败", response }
    }
  }

  /**
   * 获取请求token (bat token) - 支持强制刷新
   */
  async getRequestToken(roleId, token, did, serverId = null, forceRefresh = false) {
    // 检查永久缓存 (bat token是固定的，永不过期)
    const cacheKey = `bat_${roleId}`
    if (!forceRefresh && this.batTokenCache.has(cacheKey)) {
      const cached = this.batTokenCache.get(cacheKey)
      console.log(`📦 使用永久缓存bat token: ${roleId}`)
      return { success: true, token: cached.token }
    }

    const header = await this.getHeaders(token, null, roleId)
    header.token = token
    header.did = did
    header["b-at"] = "" // 清空bat

    const data = {
      serverId: serverId || this.SERVER_ID,
      roleId: roleId,
    }

    const url = `${this.getAvailableKuroProxy()}/aki/roleBox/requestToken`

    const response = await this.wavesRequest(url, "POST", header, data, null, "get_request_token")

    if (response.code === 200 || response.code === 10902) {
      const contentData = response.data
      let accessToken = ""

      if (typeof contentData === "string") {
        try {
          const jsonData = JSON.parse(contentData)
          accessToken = jsonData.accessToken || ""
        } catch (e) {
          console.error(`[${roleId}] 解析token失败:`, e)
        }
      } else if (typeof contentData === "object") {
        accessToken = contentData.accessToken || ""
      }

      if (accessToken) {
        // 永久缓存bat token (bat token是固定的)
        this.batTokenCache.set(cacheKey, {
          token: accessToken,
          timestamp: Date.now(), // 仅用于记录获取时间
        })

        // 保存到数据库
        try {
          // 查找用户记录
          const user = await WavesUser.selectDataByCookieAndUid(token, roleId)
          if (user) {
            // 更新bat字段
            const updatedUser = await WavesUser.createOrUpdate({
              ...user.dataValues,
              bat: accessToken,
            })
            console.log(`💾 bat token已保存到数据库: ${roleId}`)
          } else {
            console.warn(`⚠️ 未找到用户记录，无法保存bat token: ${roleId}`)
          }
        } catch (error) {
          console.error("保存bat token到数据库失败:", error)
        }

        return { success: true, token: accessToken }
      } else {
        return { success: false, message: response.msg || "获取访问令牌失败" }
      }
    } else {
      console.warn(`[${roleId}] 获取bat失败:`, response)
      return { success: false, message: response.msg || "获取访问令牌失败" }
    }
  }

  /**
   * 登录日志验证 - 按照WutheringWavesUID的实现
   */
  async loginLog(roleId, token) {
    const header = await this.getHeaders(token, null, roleId)
    header.token = token
    header.devCode = header.did || ""
    header.version = this.KURO_VERSION

    // 🔑 关键：按照WutheringWavesUID的方式，移除did和b-at字段
    delete header.did
    delete header["b-at"]

    const data = {}

    try {
      const response = await this.kuroapi.loginLog(roleId, header, data)
      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "登录验证失败" }
      }
    } catch (error) {
      console.error("登录验证失败:", error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 刷新数据 - 按照WutheringWavesUID的实现
   */
  async refreshData(roleId, token, serverId = null) {
    const header = await this.getHeaders(token, null, roleId)
    header.token = token

    // 🔑 关键：按照WutheringWavesUID的逻辑，只有当header.roleId !== roleId时才获取bat token
    if (header.roleId !== roleId) {
      const batResult = await this.getRequestToken(roleId, token, header.did || "")
      if (batResult.success) {
        header["b-at"] = batResult.token
      } else {
        return { success: false, message: batResult.message }
      }
    }

    const data = {
      gameId: this.GAME_ID,
      serverId: serverId || this.SERVER_ID,
      roleId: roleId,
    }

    try {
      const response = await this.kuroapi.refreshData(roleId, header["b-at"], token, data)
      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "刷新数据失败" }
      }
    } catch (error) {
      console.error("刷新数据失败:", error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取基础信息
   */
  async getBaseInfo(roleId, token, serverId = null) {
    const header = await this.getHeaders(token, null, roleId)
    header.token = token

    // 检查是否需要获取bat token
    if (header.roleId !== roleId) {
      const batResult = await this.getRequestToken(roleId, token, header.did || "")
      if (batResult.success) {
        header["b-at"] = batResult.token
      } else {
        return { success: false, message: batResult.message }
      }
    }

    const data = {
      gameId: this.GAME_ID,
      serverId: serverId || this.SERVER_ID,
      roleId: roleId,
    }

    const url = `${this.getAvailableKuroProxy()}/aki/roleBox/akiBox/baseData`
    const cacheKey = `base_info_${roleId}`

    const response = await this.wavesRequest(url, "POST", header, data, cacheKey)

    return this.checkResponse(response, token, roleId)
  }

  /**
   * 获取实时基础信息 - 不使用缓存，获取最新的体力、成就等数据
   */
  async getRealtimeBaseInfo(roleId, token, serverId = null, batToken = null) {
    console.log(`⚡ 获取实时基础信息 (不使用缓存): ${roleId}`)

    const header = await this.getHeaders(token, null, roleId)
    header.token = token

    // 如果提供了bat token，使用它
    if (batToken) {
      header["b-at"] = batToken
    } else {
      // 检查是否需要获取bat token
      if (header.roleId !== roleId) {
        const batResult = await this.getRequestToken(roleId, token, header.did || "")
        if (batResult.success) {
          header["b-at"] = batResult.token
        } else {
          return { success: false, message: batResult.message }
        }
      }
    }

    const data = {
      gameId: this.GAME_ID,
      serverId: serverId || this.SERVER_ID,
      roleId: roleId,
    }

    const url = `${this.getAvailableKuroProxy()}/aki/roleBox/akiBox/baseData`

    // 不使用缓存，直接请求最新数据
    const response = await this.wavesRequest(url, "POST", header, data, null)

    return this.checkResponse(response, token, roleId)
  }

  /**
   * 获取角色信息
   */
  async getRoleInfo(roleId, token, serverId = null, batToken = null) {
    const header = await this.getHeaders(token, null, roleId)
    header.token = token

    // 如果提供了bat token，使用它
    if (batToken) {
      header["b-at"] = batToken
    } else {
      // 检查是否需要获取bat token
      if (header.roleId !== roleId) {
        const batResult = await this.getRequestToken(roleId, token, header.did || "")
        if (batResult.success) {
          header["b-at"] = batResult.token
        } else {
          return { success: false, message: batResult.message }
        }
      }
    }

    const data = {
      gameId: this.GAME_ID,
      serverId: serverId || this.SERVER_ID,
      roleId: roleId,
    }

    const url = `${this.getAvailableKuroProxy()}/aki/roleBox/akiBox/roleData`
    const cacheKey = `role_info_${roleId}`

    const response = await this.wavesRequest(url, "POST", header, data, cacheKey)

    const checkResult = this.checkResponse(response, token, roleId)

    // 特殊检查：如果roleList为null，返回错误
    if (checkResult.success && checkResult.data && checkResult.data.roleList === null) {
      return { success: false, message: "角色列表为空，请检查账号绑定" }
    }

    return checkResult
  }

  /**
   * 获取角色详细信息
   */
  async getRoleDetailInfo(charId, roleId, token, serverId = null, batToken = null) {
    const header = await this.getHeaders(token, null, roleId)
    header.token = token

    // 如果提供了bat token，使用它
    if (batToken) {
      header["b-at"] = batToken
    } else {
      // 检查是否需要获取bat token
      if (header.roleId !== roleId) {
        const batResult = await this.getRequestToken(roleId, token, header.did || "")
        if (batResult.success) {
          header["b-at"] = batResult.token
        } else {
          return { success: false, message: batResult.message }
        }
      }
    }

    const data = {
      gameId: this.GAME_ID,
      serverId: serverId || this.SERVER_ID,
      roleId: roleId,
      channelId: "19",
      countryCode: "1",
      id: charId,
    }

    const url = `${this.getAvailableKuroProxy()}/aki/roleBox/akiBox/getRoleDetail`
    const cacheKey = `role_detail_${roleId}_${charId}`

    const response = await this.wavesRequest(url, "POST", header, data, cacheKey)

    return this.checkResponse(response, token)
  }

  /**
   * 获取探索数据
   */
  async getExploreData(roleId, token, serverId = null, countryCode = "1", batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.token
      }

      // 使用kuroapi获取探索数据
      const response = await this.kuroapi.getExploreData(roleId, bat, countryCode, token)

      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取探索数据失败" }
      }
    } catch (error) {
      console.error("获取探索数据失败:", error)
      return { success: false, message: `获取探索数据失败: ${error.message}` }
    }
  }

  /**
   * 获取挑战数据（全息战略）
   */
  async getChallengeData(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.token
      }

      // 使用kuroapi获取挑战数据
      const response = await this.kuroapi.getChallengeData(roleId, bat, token)

      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取挑战数据失败" }
      }
    } catch (error) {
      console.error("获取挑战数据失败:", error)
      return { success: false, message: `获取挑战数据失败: ${error.message}` }
    }
  }

  /**
   * 获取数据坞信息
   */
  async getCalabashData(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.token
      }

      // 使用kuroapi获取数据坞信息
      const response = await this.kuroapi.getCalabashData(roleId, bat, token)

      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取数据坞信息失败" }
      }
    } catch (error) {
      console.error("获取数据坞信息失败:", error)
      return { success: false, message: `获取数据坞信息失败: ${error.message}` }
    }
  }

  /**
   * 获取塔楼数据（逆境深塔）
   */
  async getTowerData(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.token
      }

      // 使用kuroapi获取塔楼数据
      const response = await this.kuroapi.getTowerData(roleId, bat, token)

      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取塔楼数据失败" }
      }
    } catch (error) {
      console.error("获取塔楼数据失败:", error)
      return { success: false, message: `获取塔楼数据失败: ${error.message}` }
    }
  }

  /**
   * 获取更多活动数据
   */
  async getMoreActivityData(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.token
      }

      // 使用kuroapi获取更多活动数据
      const response = await this.kuroapi.getMoreActivityData(roleId, bat, token)

      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取更多活动数据失败" }
      }
    } catch (error) {
      return { success: false, message: `获取更多活动数据失败: ${error.message}` }
    }
  }

  /**
   * 智能更新角色数据缓存（只更新有变化的角色）
   */
  updateRoleDataCache(uid, updatedRoleIds = []) {
    if (!updatedRoleIds || updatedRoleIds.length === 0) {
      console.log(`ℹ️ 没有角色数据更新，保留现有缓存`)
      return
    }

    const keysToDelete = []

    // 显示当前所有缓存键
    console.log(`🔍 当前缓存键: [${Array.from(this.roleDataCache.keys()).join(", ")}]`)
    console.log(`🔍 要更新的角色: ${updatedRoleIds.join(", ")}`)

    for (const key of this.roleDataCache.keys()) {
      // 检查缓存键是否包含指定的UID和更新的角色ID
      if (key.includes(uid.toString())) {
        // 检查是否是更新的角色
        const isUpdatedRole = updatedRoleIds.some(roleId => key.includes(`_${roleId}`))
        if (isUpdatedRole) {
          keysToDelete.push(key)
        }
      }
    }

    keysToDelete.forEach(key => {
      this.roleDataCache.delete(key)
      console.log(`🔄 更新缓存: ${key}`)
    })

    console.log(
      `✅ 已更新 ${keysToDelete.length} 项角色数据缓存，保留了 ${Array.from(this.roleDataCache.keys()).filter(k => k.includes(uid.toString())).length} 项未变化的缓存`,
    )
  }

  /**
   * 清除指定UID的角色数据缓存 (保留用于特殊情况)
   */
  clearRoleDataCache(uid) {
    const keysToDelete = []

    // 显示当前所有缓存键
    console.log(`🔍 当前缓存键: [${Array.from(this.roleDataCache.keys()).join(", ")}]`)
    console.log(`🔍 要清除的UID: ${uid}`)

    for (const key of this.roleDataCache.keys()) {
      // 检查缓存键是否包含指定的UID
      if (key.includes(uid.toString())) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => {
      this.roleDataCache.delete(key)
      console.log(`🗑️ 清除缓存: ${key}`)
    })

    console.log(`✅ 已清除 ${keysToDelete.length} 项角色数据缓存`)

    // 显示清除后的缓存键
    console.log(`🔍 清除后缓存键: [${Array.from(this.roleDataCache.keys()).join(", ")}]`)
  }

  /**
   * 完整刷新面板 - 智能更新，只更新有变化的数据
   */
  async refreshCharDetail(uid, token, did) {
    console.log(`🔄 开始刷新面板: ${uid}`)

    // 不再无条件清除缓存，改为智能比较和更新
    // 缓存将在保存数据后根据实际更新的角色进行智能更新

    try {
      // 1. 获取角色列表
      const roleListResult = await this.getKuroRoleList(token, did)
      if (!roleListResult.success) {
        return { success: false, message: roleListResult.message }
      }

      const roleData = roleListResult.data.find(role => role.roleId === uid)
      if (!roleData) {
        return { success: false, message: "未找到对应的角色数据" }
      }

      // 2. 获取bat token (优先使用数据库中的)
      let batToken = ""
      try {
        const userData = await WavesUser.selectDataByCookie(token)
        if (userData && userData.bat) {
          batToken = userData.bat
          console.log(`📦 使用数据库中的bat token: ${uid}`)
        }
      } catch (error) {
        console.warn("从数据库获取bat token失败:", error.message)
      }

      // 如果数据库中没有bat token，才重新获取
      if (!batToken) {
        console.log(`🔄 数据库中无bat token，重新获取: ${uid}`)
        const batResult = await this.getRequestToken(uid, token, did, roleData.serverId)
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        batToken = batResult.token
      }

      // 3. 并行执行刷新数据和获取角色信息，提高速度
      console.log(`🚀 并行执行刷新数据和获取角色信息...`)
      const [refreshResult, roleInfoResult] = await Promise.all([
        this.refreshData(uid, token, roleData.serverId, batToken),
        this.getRoleInfo(uid, token, roleData.serverId, batToken),
      ])

      if (!refreshResult.success) {
        return { success: false, message: `刷新数据失败: ${refreshResult.message}` }
      }

      if (!roleInfoResult.success) {
        return { success: false, message: `获取角色信息失败: ${roleInfoResult.message}` }
      }

      // 5. 解析角色数据
      let roleList = []
      try {
        const parsedData =
          typeof roleInfoResult.data === "string"
            ? JSON.parse(roleInfoResult.data)
            : roleInfoResult.data
        roleList = parsedData.roleList || []
      } catch (error) {
        console.error("解析角色数据失败:", error)
        return { success: false, message: "角色数据解析失败" }
      }

      // 6. 并发获取所有角色详细信息
      const semaphore = await refreshPanelManager.semaphoreManager.getSemaphore()
      const characterDetails = []

      console.log(`📊 开始并发获取 ${roleList.length} 个角色的详细信息...`)
      const startTime = Date.now()

      // 使用 Promise.allSettled 进行并发处理
      const detailPromises = roleList.map(async character => {
        await semaphore.acquire()

        try {
          const detailResult = await this.getRoleDetailInfo(
            character.roleId, // charId - 角色ID
            uid, // roleId - 玩家UID
            token,
            roleData.serverId,
            batToken,
          )

          if (detailResult.success) {
            let detailData = detailResult.data
            if (typeof detailData === "string") {
              detailData = JSON.parse(detailData)
            }
            console.log(`✅ 获取角色详情成功: ${character.roleName}`)
            return { success: true, data: detailData, character }
          } else {
            console.warn(`⚠️ 获取角色详情失败: ${character.roleName} - ${detailResult.message}`)
            return { success: false, character, error: detailResult.message }
          }
        } catch (error) {
          console.error(`❌ 获取角色详情异常: ${character.roleName}`, error)
          return { success: false, character, error: error.message }
        } finally {
          semaphore.release()
        }
      })

      // 等待所有请求完成
      const results = await Promise.allSettled(detailPromises)

      // 处理结果
      let successCount = 0
      results.forEach(result => {
        if (result.status === "fulfilled" && result.value.success) {
          characterDetails.push(result.value.data)
          successCount++
        }
      })

      const endTime = Date.now()
      console.log(
        `⚡ 并发获取完成: ${successCount}/${roleList.length} 个角色，耗时 ${((endTime - startTime) / 1000).toFixed(1)}s`,
      )

      // 7. 保存角色面板信息
      const saveResult = await refreshPanelManager.saveCardInfo(uid, characterDetails)

      if (saveResult.success) {
        console.log(`✅ 面板刷新完成: ${uid} - ${saveResult.data.total}个角色`)

        // 智能更新缓存：只更新有变化的角色缓存
        if (saveResult.data.updatedRoleIds && saveResult.data.updatedRoleIds.length > 0) {
          this.updateRoleDataCache(uid, saveResult.data.updatedRoleIds)
        } else {
          console.log(`ℹ️ 没有角色数据更新，保留现有缓存`)
        }

        return {
          success: true,
          message: `刷新成功，获取了${saveResult.data.total}个角色的面板数据`,
          data: {
            uid: uid,
            total: saveResult.data.total,
            updated: saveResult.data.updated,
            unchanged: saveResult.data.unchanged,
            updatedRoleIds: saveResult.data.updatedRoleIds || [], // 传递更新的角色ID列表
          },
        }
      } else {
        return { success: false, message: saveResult.message }
      }
    } catch (error) {
      console.error("刷新面板失败:", error)
      return { success: false, message: `刷新失败: ${error.message}` }
    }
  }

  /**
   * 检查响应
   */
  checkResponse(res, token = null, roleId = null) {
    if (typeof res === "object" && res !== null) {
      const resCode = res.code
      const resMsg = res.msg
      const resData = res.data

      if (resCode === 200 && resData) {
        return { success: true, data: resData }
      }

      if (resCode === 10902 && resData) {
        return { success: true, data: resData }
      }

      console.warn(`[wwuid] code: ${resCode} msg: ${resMsg} data: ${resData}`)

      if (resMsg) {
        return { success: false, message: resMsg }
      }
    }

    return { success: false, message: "请求失败" }
  }

  /**
   * 获取深渊索引数据
   */
  async getAbyssIndex(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.data
      }

      const response = await this.kuroapi.getAbyssIndex(roleId, bat, token)
      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取深渊索引数据失败" }
      }
    } catch (error) {
      return { success: false, message: `获取深渊索引数据失败: ${error.message}` }
    }
  }

  /**
   * 获取深渊详情数据
   */
  async getAbyssData(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.data
      }

      const response = await this.kuroapi.getAbyssData(roleId, bat, token)
      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取深渊详情数据失败" }
      }
    } catch (error) {
      return { success: false, message: `获取深渊详情数据失败: ${error.message}` }
    }
  }

  /**
   * 获取冥海索引数据
   */
  async getSlashIndex(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.data
      }

      const response = await this.kuroapi.getSlashIndex(roleId, bat, token)
      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取冥海索引数据失败" }
      }
    } catch (error) {
      return { success: false, message: `获取冥海索引数据失败: ${error.message}` }
    }
  }

  /**
   * 获取冥海详情数据
   */
  async getSlashDetail(roleId, token, serverId = null, batToken = null) {
    try {
      // 获取或使用提供的bat token
      let bat = batToken
      if (!bat) {
        const batResult = await this.getRequestToken(roleId, token, "")
        if (!batResult.success) {
          return { success: false, message: batResult.message }
        }
        bat = batResult.data
      }

      const response = await this.kuroapi.getSlashDetail(roleId, bat, token)
      if (response && (response.code === 200 || response.code === 10902)) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response?.msg || "获取冥海详情数据失败" }
      }
    } catch (error) {
      return { success: false, message: `获取冥海详情数据失败: ${error.message}` }
    }
  }
}

// 创建全局实例
const wutheringWavesAPI = new WutheringWavesAPI()

export default wutheringWavesAPI
export { WutheringWavesAPI }
