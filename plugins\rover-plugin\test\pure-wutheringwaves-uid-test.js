import axios from "axios"
import { HttpsProxyAgent } from "https-proxy-agent"

// 测试配置 - 使用用户提供的最新token
const realToken = "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY3NjA3MzMyLCJ1c2VySWQiOjEwODE2NzMyfQ.JBD_vTlMHMWgrptBvTOI05uUYFIdYITsfK9x1bTVHcw,6F02FE7B671ACA64694F19FB67EBEBAD07659846"
const realUid = "100381247"
const proxyUrl = "http://1907581050:<EMAIL>:9151"

console.log("🔍 纯WutheringWavesUID方式测试...")
console.log(`📋 使用Token: ${realToken.substring(0, 50)}...`)
console.log(`🎮 使用UID: ${realUid}`)
console.log("================================================================================")

// 完全按照WutheringWavesUID的方式实现请求头
async function getHeaders(ck = null, platform = null, queryRoleId = null) {
  if (!ck && !platform) {
    return {
      "source": "h5",
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "devCode": "randomDevCode123",
      "version": "2.5.0",
    }
  }

  // 模拟从数据库获取用户信息的逻辑
  let bat = ""
  let did = "6F02FE7B671ACA64694F19FB67EBEBAD07659846"
  let tokenRoleId = realUid
  platform = platform || "ios"

  const header = {
    "source": platform,
    "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
    "User-Agent": platform === "ios" 
      ? "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
      : "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "devCode": did,
    "version": "2.5.0",
  }

  if (bat) {
    header["b-at"] = bat
  }
  if (did) {
    header["did"] = did
  }
  if (tokenRoleId) {
    header["roleId"] = tokenRoleId
  }

  return header
}

// 完全按照WutheringWavesUID的方式实现请求
async function wavesRequest(url, method = "POST", header = null, data = null) {
  if (header) {
    // 🔑 关键：WutheringWavesUID在所有请求中都移除roleId
    delete header.roleId
    console.log(`🔑 已移除roleId字段（按照WutheringWavesUID方式）`)
  }

  const options = {
    method: method,
    url: url,
    headers: header,
    timeout: 10000,
  }

  // 配置代理
  if (proxyUrl) {
    const agent = new HttpsProxyAgent(proxyUrl)
    options.httpsAgent = agent
    console.log(`🌐 使用代理: ${proxyUrl}`)
  }

  // 设置请求体
  if (data) {
    if (data instanceof URLSearchParams) {
      options.data = data.toString()
    } else {
      const params = new URLSearchParams()
      for (const [key, value] of Object.entries(data)) {
        params.append(key, value)
      }
      options.data = params.toString()
    }
  }

  try {
    console.log(`📡 请求: ${method} ${url}`)
    console.log(`📋 请求头:`, JSON.stringify(header, null, 2))
    console.log(`📋 请求体:`, options.data)
    
    const response = await axios(options)
    console.log(`✅ 响应: code=${response.data.code}, msg=${response.data.msg}`)
    return response.data
  } catch (error) {
    console.error(`❌ 请求失败:`, error.message)
    if (error.response) {
      console.error(`❌ 响应状态:`, error.response.status)
      console.error(`❌ 响应数据:`, error.response.data)
    }
    throw error
  }
}

async function testPureWutheringWavesUID() {
  try {
    console.log("📋 测试1：按照WutheringWavesUID方式调用login_log")
    console.log("==================================================")
    
    const loginHeader = await getHeaders(realToken, "ios", realUid)
    loginHeader.token = realToken
    loginHeader.devCode = loginHeader.did || ""
    loginHeader.version = "2.5.0"
    
    // 🔑 关键：WutheringWavesUID在login_log中移除did和b-at
    delete loginHeader.did
    delete loginHeader["b-at"]
    
    try {
      const loginResult = await wavesRequest(
        "https://api.kurobbs.com/aki/roleBox/akiBox/loginLog",
        "POST",
        loginHeader,
        {}
      )
      console.log("✅ login_log成功:", loginResult)
    } catch (error) {
      console.log("❌ login_log失败:", error.message)
    }
    
    console.log("\n📋 测试2：按照WutheringWavesUID方式调用refresh_data")
    console.log("==================================================")
    
    const refreshHeader = await getHeaders(realToken, "ios", realUid)
    refreshHeader.token = realToken
    
    // 🔑 关键：WutheringWavesUID在refresh_data中检查roleId
    if (refreshHeader.roleId !== realUid) {
      console.log("🔄 需要获取bat token...")
      // 这里应该获取bat token，但我们先跳过
    }
    
    try {
      const refreshResult = await wavesRequest(
        "https://api.kurobbs.com/aki/roleBox/akiBox/refreshData",
        "POST",
        refreshHeader,
        {
          gameId: "3",
          serverId: "76402e5b20be2c39f095a152090afddc",
          roleId: realUid,
        }
      )
      console.log("✅ refresh_data成功:", refreshResult)
    } catch (error) {
      console.log("❌ refresh_data失败:", error.message)
    }
    
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message)
  }
}

// 运行测试
testPureWutheringWavesUID()
  .then(() => {
    console.log("\n✨ 纯WutheringWavesUID方式测试完成！")
    console.log("💡 请检查是否完全按照WutheringWavesUID的方式执行")
  })
  .catch(error => {
    console.error("❌ 测试过程中发生错误:", error)
  })
