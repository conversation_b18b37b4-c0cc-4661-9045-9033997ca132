import wutheringWavesAPI from "../components/WutheringWavesAPI.js"

// 测试配置 - 使用用户提供的最新token
const realToken =
  "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY3NjA3MzMyLCJ1c2VySWQiOjEwODE2NzMyfQ.JBD_vTlMHMWgrptBvTOI05uUYFIdYITsfK9x1bTVHcw,6F02FE7B671ACA64694F19FB67EBEBAD07659846"
const realUid = "100381247"

console.log("🔍 测试WutheringWavesUID完整流程...")
console.log(`📋 使用Token: ${realToken}`)
console.log(`🎮 使用UID: ${realUid}`)
console.log("================================================================================")

async function testWutheringWavesUIDFlow() {
  try {
    console.log("📋 第一步：模拟WutheringWavesUID的token验证流程")
    console.log("==================================================")

    // 1. 首先调用login_log验证token（WutheringWavesUID的标准流程）
    console.log("🔄 步骤1: 调用login_log验证token...")
    try {
      const loginResult = await wutheringWavesAPI.loginLog(realUid, realToken)
      if (loginResult && loginResult.success) {
        console.log("✅ login_log成功")
      } else {
        console.log("❌ login_log失败:", loginResult?.message || "未知错误")
      }
    } catch (error) {
      console.log("❌ login_log异常:", error.message)
    }

    // 2. 然后调用refresh_data验证数据访问权限
    console.log("\n🔄 步骤2: 调用refresh_data验证数据访问权限...")
    try {
      const refreshResult = await wutheringWavesAPI.refreshData(realUid, realToken)
      if (refreshResult && refreshResult.success) {
        console.log("✅ refresh_data成功")
      } else {
        console.log("❌ refresh_data失败:", refreshResult?.message || "未知错误")
      }
    } catch (error) {
      console.log("❌ refresh_data异常:", error.message)
    }

    console.log("\n📋 第二步：在验证通过后调用数据API")
    console.log("==================================================")

    // 3. 只有在验证通过后才调用数据API
    console.log("🔄 步骤3: 调用getBaseInfo获取基础数据...")
    try {
      const baseResult = await wutheringWavesAPI.getBaseInfo(realUid, realToken)
      if (baseResult && baseResult.success) {
        console.log("✅ getBaseInfo成功")
        console.log(`📊 数据大小: ${JSON.stringify(baseResult.data).length} 字符`)
      } else {
        console.log("❌ getBaseInfo失败:", baseResult?.message || "未知错误")
      }
    } catch (error) {
      console.log("❌ getBaseInfo异常:", error.message)
    }

    // 4. 测试一个扩展API
    console.log("\n🔄 步骤4: 调用getExploreData获取探索数据...")
    try {
      const exploreResult = await wutheringWavesAPI.getExploreData(realUid, realToken)
      if (exploreResult && exploreResult.success) {
        console.log("✅ getExploreData成功")
        console.log(`📊 数据大小: ${JSON.stringify(exploreResult.data).length} 字符`)
      } else {
        console.log("❌ getExploreData失败:", exploreResult?.message || "未知错误")
      }
    } catch (error) {
      console.log("❌ getExploreData异常:", error.message)
    }
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message)
  }
}

// 运行测试
testWutheringWavesUIDFlow()
  .then(() => {
    console.log("\n✨ WutheringWavesUID流程测试完成！")
    console.log("💡 请检查是否按照WutheringWavesUID的标准流程执行")
  })
  .catch(error => {
    console.error("❌ 测试过程中发生错误:", error)
  })
