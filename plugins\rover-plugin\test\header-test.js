import wutheringWavesAPI from "../components/WutheringWavesAPI.js"

// 测试配置
const realToken =
  "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY2Mjg1NjExLCJ1c2VySWQiOjEwODE2NzMyfQ.h-IVfX8F7IJkPISy016LT6_xh7iaxNWnVKCHwzOqg-M"
const realUid = "100381247"

console.log("🔍 测试请求头处理和roleId字段移除...")
console.log(`📋 使用Token: ${realToken}`)
console.log(`🎮 使用UID: ${realUid}`)
console.log("================================================================================")

async function testHeaderProcessing() {
  try {
    console.log("🔄 测试KuroAPI的makeRequest方法，观察roleId字段处理...")

    // 直接测试KuroAPI的makeRequest方法
    const KuroApi = (await import("../components/api/kuroapi.js")).default
    const kuroApi = new KuroApi()

    // 创建包含roleId的测试headers
    const testHeaders = {
      source: "ios",
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent":
        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
      token: realToken,
      roleId: realUid, // 手动添加roleId字段
      version: "2.5.0",
    }

    console.log("📋 测试headers包含roleId字段:", testHeaders.roleId)

    // 测试一个简单的请求
    const testBody = new URLSearchParams({
      gameId: "3",
      serverId: "76402e5b20be2c39f095a152090afddc",
      roleId: realUid,
    })

    const result = await kuroApi.makeRequest(
      "https://api.kurobbs.com/aki/roleBox/akiBox/baseData",
      testHeaders,
      testBody,
      "test_function",
    )

    if (result && (result.code === 200 || result.code === 10902)) {
      console.log("✅ API调用成功")
      console.log(`📊 响应代码: ${result.code}`)
    } else {
      console.log("❌ API调用失败:", result)
    }
  } catch (error) {
    console.error("❌ 测试失败:", error.message)
  }
}

// 运行测试
testHeaderProcessing()
  .then(() => {
    console.log("✨ 请求头测试完成！")
    console.log("💡 请检查上面的日志，确认是否看到 '🔑 已移除roleId字段以避免用户掉线' 的消息")
  })
  .catch(error => {
    console.error("❌ 测试过程中发生错误:", error)
  })
