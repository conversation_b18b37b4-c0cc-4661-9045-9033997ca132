import axios from "axios"
import { HttpsProxyAgent } from "https-proxy-agent"
import { API_CONFIG } from "../../utils/constants.js"
import { getPublicIp } from "../../utils/network.js"
import { generateUUID } from "../../utils/tools.js"
import config from "../config.js"

// 获取完整URL
const getUrl = path => `${API_CONFIG.BASE_URL}${path}`

// API接口路径
const URI = {
  GET_SMS_CODE: "/user/getSmsCode", // 获取验证码
  SDK_LOGIN: "/user/sdkLogin", // 登录
  SDK_LOGIN_H5: "/user/sdkLoginForH5", // H5登录
  ROLE_LIST: "/gamer/role/list", // 角色列表
  LOGIN_LOG: "/user/login/log", // 登录日志
  REQUEST_TOKEN_URL: "/aki/roleBox/requestToken", // 获取bat token
  BASE_DATA: "/aki/roleBox/akiBox/baseData", // 基础数据
  ROLE_DATA: "/aki/roleBox/akiBox/roleData", // 角色数据
  ROLE_DETAIL: "/aki/roleBox/akiBox/getRoleDetail", // 角色详情
  CALABASH_DATA: "/aki/roleBox/akiBox/calabashData", // 数据坞信息
  CHALLENGE_DATA: "/aki/roleBox/akiBox/challengeDetails", // 挑战详情
  CHALLENGE_INDEX: "/aki/roleBox/akiBox/challengeIndex", // 挑战索引
  EXPLORE_DATA: "/aki/roleBox/akiBox/exploreIndex", // 探索数据
  TOWER_INDEX: "/aki/roleBox/akiBox/towerIndex", // 塔楼索引
  TOWER_DETAIL: "/aki/roleBox/akiBox/towerDataDetail", // 塔楼详情
  SLASH_INDEX: "/aki/roleBox/akiBox/slashIndex", // 斩击索引
  SLASH_DETAIL: "/aki/roleBox/akiBox/slashDetail", // 斩击详情
  MORE_ACTIVITY: "/aki/roleBox/akiBox/moreActivity", // 更多活动
  REFRESH_DATA: "/aki/roleBox/akiBox/refreshData", // 刷新数据
  SIGNIN_URL: "/encourage/signIn/v2", // 签到
  SIGNIN_TASK_LIST: "/encourage/signIn/initSignInV2", // 签到任务列表
  // 计算器相关
  CALCULATOR_REFRESH: "/aki/calculator/refreshData", // 计算器刷新数据
  ONLINE_LIST_ROLE: "/aki/calculator/listRole", // 在线角色列表
  ONLINE_LIST_WEAPON: "/aki/calculator/listWeapon", // 在线武器列表
  ONLINE_LIST_PHANTOM: "/aki/calculator/listPhantom", // 在线声骸列表
  ROLE_CULTIVATE_STATUS: "/aki/calculator/roleCultivateStatus", // 角色培养状态
  BATCH_ROLE_COST: "/aki/calculator/batchRoleCost", // 角色培养成本
  BATCH_WEAPON_COST: "/aki/calculator/batchWeaponCost", // 武器培养成本
  BATCH_PHANTOM_COST: "/aki/calculator/batchPhantomCost", // 声骸培养成本
  QUERY_OWNED_ROLE: "/aki/calculator/queryOwnedRole", // 已拥有角色
  // 资源相关
  PERIOD_LIST: "/aki/resource/period/list", // 资源周期列表
  MONTH_LIST: "/aki/resource/month", // 月度资源
  WEEK_LIST: "/aki/resource/week", // 周度资源
  VERSION_LIST: "/aki/resource/version", // 版本资源
}

// 库街区API接口类 - 使用 axios 实现，完全模拟 WutheringWavesUID
class KuroApi {
  constructor() {
    this.defaultHeaders = {
      source: API_CONFIG.SOURCE,
      "Content-Type": API_CONFIG.CONTENT_TYPE,
      "User-Agent": API_CONFIG.USER_AGENT,
      version: API_CONFIG.VERSION,
    }
  }

  // 生成设备码 - 按照WutheringWavesUID的get_headers_ios方式
  async getDevCode() {
    const ip = await getPublicIp()
    const userAgent =
      "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
    return `${ip}, ${userAgent}`
  }

  // 生成随机字符串 - 按照WutheringWavesUID的方式（32位）
  generateRandomString(length = 32) {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
    let result = ""
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // 获取通用请求头 - 按照WutheringWavesUID的get_common_header方式
  async getCommonHeader(platform = "ios") {
    const devCode = this.generateRandomString()
    return {
      source: platform,
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent":
        platform === "ios"
          ? "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
          : "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      devCode: devCode,
      version: "2.5.0",
    }
  }

  // 获取iOS设备码请求头 - 按照WutheringWavesUID的get_headers_ios方式
  async getHeadersIOS() {
    const ip = await getPublicIp()
    const userAgent =
      "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
    return {
      source: "ios",
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent": userAgent,
      devCode: `${ip}, ${userAgent}`,
      version: "2.5.0",
    }
  }

  // 获取服务器ID
  getServerId(wavesId) {
    return API_CONFIG.SERVER_ID
  }

  // 通用请求方法，使用 axios 完全模拟 WutheringWavesUID
  async makeRequest(url, headers, body, funcName = "unknown", forceProxy = null) {
    let needProxy
    if (forceProxy !== null) {
      needProxy = forceProxy
    } else {
      needProxy = config.needProxy(funcName)
    }

    const proxyUrl = needProxy ? config.getLocalProxyUrl() : null

    // 如果需要代理但没有配置代理URL，则使用直连
    const actuallyUseProxy = needProxy && proxyUrl

    console.log(`📡 请求: POST ${url}`)
    if (actuallyUseProxy) {
      console.log(`🌐 使用代理: ${proxyUrl}`)
    } else {
      if (needProxy && !proxyUrl) {
        console.log(`⚠️ 需要代理但未配置代理URL，使用直连`)
      } else {
        console.log(`🌐 直连请求`)
      }
    }

    // 🔑 关键修复：按照WutheringWavesUID的方式，在发送请求前移除roleId字段
    // 这是避免用户掉线的关键步骤！
    const cleanHeaders = { ...headers }
    if (cleanHeaders.roleId) {
      delete cleanHeaders.roleId
      console.log(`🔑 已移除roleId字段以避免用户掉线`)
    }

    const options = {
      headers: cleanHeaders, // 使用清理后的headers
      timeout: 10000,
    }

    // 配置代理
    if (actuallyUseProxy) {
      try {
        const agent = new HttpsProxyAgent(proxyUrl)
        options.httpsAgent = agent
        console.log(`🌐 代理请求: ${proxyUrl}`)
      } catch (error) {
        console.warn("代理配置失败，使用直连:", error.message)
      }
    }

    try {
      const response = await axios.post(url, body, options)
      return response.data
    } catch (error) {
      if (error.response) {
        return error.response.data
      }
      throw error
    }
  }

  // 获取bat token - 按照WutheringWavesUID的get_request_token方式
  async getRequestToken(wavesId, token, did, serverId = null) {
    const url = getUrl(URI.REQUEST_TOKEN_URL)

    // 按照WutheringWavesUID的方式获取头部
    const headers = await this.getCommonHeader("ios")
    headers.token = token
    headers.did = did
    headers["b-at"] = "" // 清空bat

    const body = new URLSearchParams({
      serverId: serverId || this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      console.log(`🔑 获取bat token: 角色=${wavesId}`)
      const response = await this.makeRequest(url, headers, body, "getRequestToken")

      if (response.code === 200 || response.code === 10902) {
        let accessToken = ""
        const contentData = response.data

        if (typeof contentData === "string") {
          try {
            const jsonData = JSON.parse(contentData)
            accessToken = jsonData.accessToken || ""
          } catch (e) {
            console.error(`解析bat token失败: ${e}`)
          }
        } else if (typeof contentData === "object") {
          accessToken = contentData.accessToken || ""
        }

        if (accessToken) {
          console.log(`🔑 bat token获取成功: ${accessToken.substring(0, 10)}...`)
        }
        return accessToken
      } else {
        console.warn("获取bat token失败:", response)
        return ""
      }
    } catch (error) {
      console.error("获取bat token失败:", error)
      return ""
    }
  }

  async getLoginLog(wavesId, token, did) {
    const url = getUrl(URI.LOGIN_LOG)

    const headers = {
      ...this.defaultHeaders,
      token: token,
      devCode: did,
    }

    const body = new URLSearchParams()

    try {
      const response = await this.makeRequest(url, headers, body, "getLoginLog")
      return response
    } catch (error) {
      console.error("获取登录日志失败:", error)
      throw error
    }
  }

  // 验证码登录 - 按照WutheringWavesUID的login方式
  async sdkLogin(mobile, code, did) {
    if (!did) {
      did = await this.getDevCode()
    }

    const url = getUrl(URI.SDK_LOGIN)
    // 使用iOS平台的头部，按照WutheringWavesUID的login_platform()返回"ios"
    const headers = await this.getHeadersIOS()

    const body = new URLSearchParams({
      mobile: mobile,
      code: code,
      devCode: did,
    })

    try {
      console.log(`🔐 尝试登录: 手机号=${mobile}, 设备ID=${did}`)
      console.log(`🔐 使用iOS平台登录，User-Agent=${headers["User-Agent"]}`)
      const response = await this.makeRequest(url, headers, body, "sdkLogin")
      return response
    } catch (error) {
      console.error("登录失败:", error)
      throw error
    }
  }

  // 获取用户绑定的游戏角色列表 - 完全按照WutheringWavesUID的get_kuro_role_list
  async getRoleList(token, did) {
    const url = getUrl(URI.ROLE_LIST)

    // 按照WutheringWavesUID的方式：使用login_platform()返回"ios"，然后使用get_common_header
    const platform = "ios" // login_platform() 返回 "ios"
    const headers = await this.getCommonHeader(platform)

    // 按照WutheringWavesUID的方式设置请求头
    headers.token = token
    headers.devCode = did

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
    })

    try {
      console.log(`🔍 [KuroAPI] 获取角色列表请求`)
      console.log(`🔍 [KuroAPI] URL: ${url}`)
      console.log(`🔍 [KuroAPI] Token: ${token.substring(0, 20)}...`)
      console.log(`🔍 [KuroAPI] DID: ${did}`)
      console.log(`🔍 [KuroAPI] Platform: ${platform}`)

      const response = await this.makeRequest(url, headers, body, "get_kuro_role_list")

      // 检查风险检测
      if (response && response.code === 270) {
        console.warn(`⚠️ [KuroAPI] 检测到风险控制: ${response.msg}`)
        console.warn(`⚠️ [KuroAPI] 建议：1. 更换代理IP 2. 等待一段时间后重试 3. 检查请求频率`)

        // 返回特殊的错误信息，让调用方知道这是风险检测
        return {
          code: 270,
          msg: response.msg,
          isRiskDetection: true,
          suggestion: "当前网络环境被标记为风险，建议更换网络环境或稍后重试",
        }
      }

      return response
    } catch (error) {
      console.error("❌ [KuroAPI] 获取角色列表失败:", error)
      throw error
    }
  }

  // 获取鸣潮角色基础数据 - 使用 WutheringWavesUID 风格
  async getBaseData(wavesId, token, bat) {
    const url = getUrl(URI.BASE_DATA)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers.token = token
    headers["b-at"] = bat

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getBaseData")
      return response
    } catch (error) {
      console.error("获取基础数据失败:", error)
      throw error
    }
  }

  // 获取角色数据 - 使用 WutheringWavesUID 风格
  async getRoleData(wavesId, bat) {
    const url = getUrl(URI.ROLE_DATA)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getRoleData")
      return response
    } catch (error) {
      console.error("获取角色数据失败:", error)
      throw error
    }
  }

  // 获取角色详细信息 - 使用 WutheringWavesUID 风格
  async getRoleDetail(wavesId, charId, bat) {
    const url = getUrl(URI.ROLE_DETAIL)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
      channelId: "19",
      countryCode: "1",
      id: charId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "get_role_detail_info")
      return response
    } catch (error) {
      console.error("获取角色详细信息失败:", error)
      throw error
    }
  }

  // 获取数据坞信息 - 使用 WutheringWavesUID 风格
  async getCalabashData(wavesId, bat, token = null) {
    const url = getUrl(URI.CALABASH_DATA)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat

    // 如果提供了token，添加到请求头
    if (token) {
      headers.token = token
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "get_calabash_data")
      return response
    } catch (error) {
      console.error("获取数据坞信息失败:", error)
      throw error
    }
  }

  // 获取挑战详情 - 使用 WutheringWavesUID 风格
  async getChallengeData(wavesId, bat, token = null) {
    const url = getUrl(URI.CHALLENGE_DATA)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat

    // 如果提供了token，添加到请求头
    if (token) {
      headers.token = token
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "get_challenge_data")
      return response
    } catch (error) {
      console.error("获取挑战详情失败:", error)
      throw error
    }
  }

  // 获取探索数据 - 使用 WutheringWavesUID 风格
  async getExploreData(wavesId, bat, countryCode = "1", token = null) {
    const url = getUrl(URI.EXPLORE_DATA)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat

    // 如果提供了token，添加到请求头
    if (token) {
      headers.token = token
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
      countryCode: countryCode,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "get_explore_data")
      return response
    } catch (error) {
      console.error("获取探索数据失败:", error)
      throw error
    }
  }

  // 获取塔楼数据 - 使用 WutheringWavesUID 风格
  async getTowerData(wavesId, bat, token = null) {
    const url = getUrl(URI.TOWER_INDEX)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat

    // 如果提供了token，添加到请求头
    if (token) {
      headers.token = token
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "get_tower_data")
      return response
    } catch (error) {
      console.error("获取塔楼数据失败:", error)
      throw error
    }
  }

  // 获取更多活动数据 - 使用 WutheringWavesUID 风格
  async getMoreActivityData(wavesId, bat, token = null) {
    const url = getUrl(URI.MORE_ACTIVITY)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat

    // 如果提供了token，添加到请求头
    if (token) {
      headers.token = token
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "get_more_activity_data")
      return response
    } catch (error) {
      console.error("获取更多活动数据失败:", error)
      throw error
    }
  }

  // 获取深渊索引数据 - 使用 WutheringWavesUID 风格
  async getAbyssIndex(wavesId, bat, token = null) {
    const url = getUrl(URI.TOWER_INDEX)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat
    if (token) headers.token = token

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getAbyssIndex")
      return response
    } catch (error) {
      console.error("获取深渊索引数据失败:", error)
      throw error
    }
  }

  // 获取深渊详情数据 - 使用 WutheringWavesUID 风格
  async getAbyssData(wavesId, bat, token = null) {
    const url = getUrl(URI.TOWER_DETAIL)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat
    if (token) headers.token = token

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getAbyssData")
      return response
    } catch (error) {
      console.error("获取深渊详情数据失败:", error)
      throw error
    }
  }

  // 获取冥海索引数据 - 使用 WutheringWavesUID 风格
  async getSlashIndex(wavesId, bat, token = null) {
    const url = getUrl(URI.SLASH_INDEX)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat
    if (token) headers.token = token

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getSlashIndex")
      return response
    } catch (error) {
      console.error("获取冥海索引数据失败:", error)
      throw error
    }
  }

  // 获取冥海详情数据 - 使用 WutheringWavesUID 风格
  async getSlashDetail(wavesId, bat, token = null) {
    const url = getUrl(URI.SLASH_DETAIL)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    headers["b-at"] = bat
    if (token) headers.token = token

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getSlashDetail")
      return response
    } catch (error) {
      console.error("获取冥海详情数据失败:", error)
      throw error
    }
  }

  // 登录日志验证 - 按照WutheringWavesUID的实现
  async loginLog(roleId, header, data) {
    const url = getUrl("/aki/roleBox/akiBox/loginLog")

    try {
      const response = await this.makeRequest(url, "POST", header, data)
      return response
    } catch (error) {
      console.error("登录日志验证失败:", error)
      throw error
    }
  }

  // 刷新数据 - 使用 WutheringWavesUID 风格
  async refreshData(wavesId, bat, token, requestData) {
    const url = getUrl(URI.REFRESH_DATA)

    // 使用 WutheringWavesUID 风格的请求头
    const headers = await this.getCommonHeader("ios")
    if (bat) {
      headers["b-at"] = bat
    }
    if (token) {
      headers["token"] = token
    }

    const body = new URLSearchParams({
      gameId: requestData?.gameId || API_CONFIG.GAME_ID,
      serverId: requestData?.serverId || this.getServerId(wavesId),
      roleId: requestData?.roleId || wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "refresh_data")
      return response
    } catch (error) {
      console.error("刷新数据失败:", error)
      throw error
    }
  }
}

export default KuroApi
