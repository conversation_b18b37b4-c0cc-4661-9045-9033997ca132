import axios from "axios"
import { HttpsProxyAgent } from "https-proxy-agent"

// 测试配置 - 使用用户提供的最新token
const realToken = "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY3NjA3MzMyLCJ1c2VySWQiOjEwODE2NzMyfQ.JBD_vTlMHMWgrptBvTOI05uUYFIdYITsfK9x1bTVHcw,6F02FE7B671ACA64694F19FB67EBEBAD07659846"
const proxyUrl = "http://1907581050:<EMAIL>:9151"

console.log("🔍 最小化WutheringWavesUID测试...")
console.log(`📋 使用Token: ${realToken.substring(0, 50)}...`)
console.log("================================================================================")

// 完全按照WutheringWavesUID的方式实现请求
async function makeWavesRequest(url, method = "POST", headers = {}, data = {}) {
  const options = {
    method: method,
    url: url,
    headers: headers,
    timeout: 10000,
  }

  // 配置代理
  if (proxyUrl) {
    const agent = new HttpsProxyAgent(proxyUrl)
    options.httpsAgent = agent
  }

  // 设置请求体
  if (data && Object.keys(data).length > 0) {
    const params = new URLSearchParams()
    for (const [key, value] of Object.entries(data)) {
      params.append(key, value)
    }
    options.data = params.toString()
  }

  try {
    console.log(`📡 请求: ${method} ${url}`)
    console.log(`📋 请求头:`, JSON.stringify(headers, null, 2))
    if (options.data) {
      console.log(`📋 请求体:`, options.data)
    }
    
    const response = await axios(options)
    console.log(`✅ 响应: code=${response.data.code}, msg=${response.data.msg}`)
    return response.data
  } catch (error) {
    console.error(`❌ 请求失败:`, error.message)
    if (error.response) {
      console.error(`❌ 响应状态:`, error.response.status)
      console.error(`❌ 响应数据:`, error.response.data)
    }
    throw error
  }
}

async function testMinimalWutheringWavesUID() {
  try {
    // 从token中提取信息
    const tokenParts = realToken.split(',')
    const jwtToken = tokenParts[0]
    const did = tokenParts.length >= 2 ? tokenParts[1] : ""
    
    console.log(`🔍 JWT Token: ${jwtToken.substring(0, 50)}...`)
    console.log(`🔍 DID: ${did}`)
    
    // 解析JWT获取用户信息
    try {
      const jwtParts = jwtToken.split('.')
      if (jwtParts.length === 3) {
        const payload = JSON.parse(atob(jwtParts[1]))
        console.log(`🔍 JWT载荷:`, payload)
        console.log(`🔍 用户ID: ${payload.userId}`)
        console.log(`🔍 创建时间: ${new Date(payload.created).toLocaleString()}`)
      }
    } catch (error) {
      console.log("❌ JWT解析失败:", error.message)
    }
    
    console.log("\n📋 测试1：获取角色列表（不需要roleId）")
    console.log("==================================================")
    
    // 1. 首先获取角色列表，这个API不需要roleId
    const roleListHeaders = {
      "source": "ios",
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
      "devCode": did,
      "version": "2.5.0",
      "token": realToken,
    }
    
    try {
      const roleListResult = await makeWavesRequest(
        "https://api.kurobbs.com/user/role/list",
        "POST",
        roleListHeaders,
        { gameId: "3" }
      )
      
      if (roleListResult && roleListResult.code === 200 && roleListResult.data) {
        console.log("✅ 获取角色列表成功!")
        console.log(`📊 角色数量: ${roleListResult.data.length}`)
        
        // 显示所有角色信息
        roleListResult.data.forEach((role, index) => {
          console.log(`\n🎮 角色 ${index + 1}:`)
          console.log(`   - 角色ID: ${role.roleId}`)
          console.log(`   - 服务器ID: ${role.serverId}`)
          console.log(`   - 服务器名称: ${role.serverName}`)
          console.log(`   - 角色名称: ${role.roleName}`)
          console.log(`   - 等级: ${role.level}`)
          console.log(`   - 是否默认: ${role.isDefault}`)
        })
        
        // 使用第一个角色进行后续测试
        if (roleListResult.data.length > 0) {
          const firstRole = roleListResult.data[0]
          console.log(`\n🎯 使用第一个角色进行测试:`)
          console.log(`   - 角色ID: ${firstRole.roleId}`)
          console.log(`   - 服务器ID: ${firstRole.serverId}`)
          
          console.log("\n📋 测试2：使用正确的角色信息调用login_log")
          console.log("==================================================")
          
          // 2. 使用正确的角色信息调用login_log
          const loginHeaders = {
            "source": "ios",
            "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
            "devCode": did,
            "version": "2.5.0",
            "token": realToken,
          }
          
          // 🔑 关键：按照WutheringWavesUID的方式，移除did和b-at字段
          delete loginHeaders.did
          delete loginHeaders["b-at"]
          
          try {
            const loginResult = await makeWavesRequest(
              "https://api.kurobbs.com/aki/roleBox/akiBox/loginLog",
              "POST",
              loginHeaders,
              {}
            )
            
            if (loginResult && (loginResult.code === 200 || loginResult.code === 10902)) {
              console.log("✅ login_log成功!")
              
              console.log("\n📋 测试3：使用正确的角色信息调用refresh_data")
              console.log("==================================================")
              
              // 3. 使用正确的角色信息调用refresh_data
              const refreshHeaders = {
                "source": "ios",
                "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
                "devCode": did,
                "version": "2.5.0",
                "did": did,
                "token": realToken,
              }
              
              // 🔑 关键：移除roleId字段
              delete refreshHeaders.roleId
              
              try {
                const refreshResult = await makeWavesRequest(
                  "https://api.kurobbs.com/aki/roleBox/akiBox/refreshData",
                  "POST",
                  refreshHeaders,
                  {
                    gameId: "3",
                    serverId: firstRole.serverId,
                    roleId: firstRole.roleId,
                  }
                )
                
                if (refreshResult && (refreshResult.code === 200 || refreshResult.code === 10902)) {
                  console.log("✅ refresh_data成功!")
                  console.log("🎉 完整的WutheringWavesUID流程测试成功！")
                } else {
                  console.log("❌ refresh_data失败:", refreshResult?.msg || "未知错误")
                }
                
              } catch (error) {
                console.log("❌ refresh_data异常:", error.message)
              }
              
            } else {
              console.log("❌ login_log失败:", loginResult?.msg || "未知错误")
            }
            
          } catch (error) {
            console.log("❌ login_log异常:", error.message)
          }
          
        } else {
          console.log("❌ 没有找到任何角色")
        }
        
      } else {
        console.log("❌ 获取角色列表失败:", roleListResult?.msg || "未知错误")
        console.log("📊 完整响应:", JSON.stringify(roleListResult, null, 2))
      }
      
    } catch (error) {
      console.log("❌ 获取角色列表异常:", error.message)
    }
    
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message)
  }
}

// 运行测试
testMinimalWutheringWavesUID()
  .then(() => {
    console.log("\n✨ 最小化WutheringWavesUID测试完成！")
    console.log("💡 请检查是否成功获取了角色列表并完成了验证流程")
  })
  .catch(error => {
    console.error("❌ 测试过程中发生错误:", error)
  })
