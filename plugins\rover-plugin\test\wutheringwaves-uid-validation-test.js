import wutheringWavesAPI from "../components/WutheringWavesAPI.js"

// 测试配置 - 使用用户提供的最新token
const realToken = "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzOTY3NjA3MzMyLCJ1c2VySWQiOjEwODE2NzMyfQ.JBD_vTlMHMWgrptBvTOI05uUYFIdYITsfK9x1bTVHcw,6F02FE7B671ACA64694F19FB67EBEBAD07659846"
const realUid = "100381247"

console.log("🔍 WutheringWavesUID验证流程测试...")
console.log(`📋 使用Token: ${realToken.substring(0, 50)}...`)
console.log(`🎮 使用UID: ${realUid}`)
console.log("================================================================================")

async function testWutheringWavesUIDValidation() {
  try {
    console.log("📋 步骤1：按照WutheringWavesUID的方式验证token")
    console.log("==================================================")
    
    // 1. 首先调用login_log验证token（WutheringWavesUID的标准流程）
    console.log("🔄 调用login_log验证token...")
    try {
      const loginResult = await wutheringWavesAPI.loginLog(realUid, realToken)
      console.log("📊 login_log结果:", JSON.stringify(loginResult, null, 2))
      
      if (loginResult && loginResult.success) {
        console.log("✅ login_log验证成功")
      } else {
        console.log("❌ login_log验证失败:", loginResult?.message || "未知错误")
        
        // 检查是否是"登录已过期"错误
        if (loginResult?.message && loginResult.message.includes("登录已过期")) {
          console.log("🚨 检测到登录已过期，这说明token已经被服务器标记为无效")
          console.log("💡 这可能是因为之前的API调用导致的")
          return
        }
      }
    } catch (error) {
      console.log("❌ login_log异常:", error.message)
      return
    }
    
    // 2. 然后调用refresh_data验证数据访问权限
    console.log("\n🔄 调用refresh_data验证数据访问权限...")
    try {
      const refreshResult = await wutheringWavesAPI.refreshData(realUid, realToken)
      console.log("📊 refresh_data结果:", JSON.stringify(refreshResult, null, 2))
      
      if (refreshResult && refreshResult.success) {
        console.log("✅ refresh_data验证成功")
      } else {
        console.log("❌ refresh_data验证失败:", refreshResult?.message || "未知错误")
        
        // 检查是否是"登录已过期"错误
        if (refreshResult?.message && refreshResult.message.includes("登录已过期")) {
          console.log("🚨 检测到登录已过期，这说明token已经被服务器标记为无效")
          console.log("💡 这可能是因为之前的API调用导致的")
          return
        }
      }
    } catch (error) {
      console.log("❌ refresh_data异常:", error.message)
      return
    }
    
    console.log("\n📋 步骤2：如果验证通过，尝试调用数据API")
    console.log("==================================================")
    
    // 3. 只有在验证通过后才调用数据API
    console.log("🔄 调用getBaseInfo获取基础数据...")
    try {
      const baseResult = await wutheringWavesAPI.getBaseInfo(realUid, realToken)
      console.log("📊 getBaseInfo结果:", JSON.stringify(baseResult, null, 2))
      
      if (baseResult && baseResult.success) {
        console.log("✅ getBaseInfo成功")
        console.log(`📊 数据大小: ${JSON.stringify(baseResult.data).length} 字符`)
      } else {
        console.log("❌ getBaseInfo失败:", baseResult?.message || "未知错误")
      }
    } catch (error) {
      console.log("❌ getBaseInfo异常:", error.message)
    }
    
    console.log("\n📋 步骤3：分析token状态")
    console.log("==================================================")
    
    // 分析token
    const tokenParts = realToken.split(',')
    if (tokenParts.length >= 2) {
      console.log(`🔍 JWT部分: ${tokenParts[0].substring(0, 50)}...`)
      console.log(`🔍 DID部分: ${tokenParts[1]}`)
      
      try {
        const jwtParts = tokenParts[0].split('.')
        if (jwtParts.length === 3) {
          const payload = JSON.parse(atob(jwtParts[1]))
          console.log(`🔍 JWT载荷:`, payload)
          
          const now = Date.now()
          const created = payload.created
          const ageMinutes = (now - created) / (1000 * 60)
          console.log(`🔍 Token年龄: ${ageMinutes.toFixed(2)} 分钟`)
          
          if (ageMinutes < 60) {
            console.log("✅ Token年龄正常，应该是有效的")
          } else {
            console.log("⚠️ Token年龄较大，可能已过期")
          }
        }
      } catch (error) {
        console.log("❌ JWT解析失败:", error.message)
      }
    }
    
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message)
  }
}

// 运行测试
testWutheringWavesUIDValidation()
  .then(() => {
    console.log("\n✨ WutheringWavesUID验证流程测试完成！")
    console.log("💡 请检查验证流程是否按照WutheringWavesUID的标准执行")
    console.log("🔧 如果token已过期，说明我们的实现仍然存在问题")
  })
  .catch(error => {
    console.error("❌ 测试过程中发生错误:", error)
  })
