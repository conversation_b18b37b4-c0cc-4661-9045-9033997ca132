import { WutheringWavesAPI } from '../components/WutheringWavesAPI.js';

// 测试配置
const TEST_CONFIG = {
    uid: '100381247',
    token: 'eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c'
};

// 测试优化后的扩展API
async function testOptimizedExtendedAPIs() {
    console.log('\n=== 测试优化后的扩展API ===');
    console.log(`使用 UID: ${TEST_CONFIG.uid}`);
    
    const api = new WutheringWavesAPI();
    
    // 首先获取 bat token
    console.log('\n1. 获取 bat token...');
    const batResult = await api.getRequestToken(TEST_CONFIG.uid, TEST_CONFIG.token, '');
    if (!batResult.success) {
        console.log('❌ bat token 获取失败:', batResult.message);
        return;
    }
    console.log('✅ bat token 获取成功');
    const batToken = batResult.token;
    
    // 测试所有扩展API
    const extendedAPIs = [
        {
            name: '探索数据',
            method: 'getExploreData',
            params: [TEST_CONFIG.uid, TEST_CONFIG.token, null, '1', batToken]
        },
        {
            name: '挑战数据',
            method: 'getChallengeData', 
            params: [TEST_CONFIG.uid, TEST_CONFIG.token, null, batToken]
        },
        {
            name: '数据坞信息',
            method: 'getCalabashData',
            params: [TEST_CONFIG.uid, TEST_CONFIG.token, null, batToken]
        },
        {
            name: '塔楼数据',
            method: 'getTowerData',
            params: [TEST_CONFIG.uid, TEST_CONFIG.token, null, batToken]
        },
        {
            name: '更多活动数据',
            method: 'getMoreActivityData',
            params: [TEST_CONFIG.uid, TEST_CONFIG.token, null, batToken]
        }
    ];
    
    console.log('\n2. 测试扩展API...');
    let successCount = 0;
    let totalCount = extendedAPIs.length;
    
    for (const apiTest of extendedAPIs) {
        try {
            console.log(`\n测试 ${apiTest.name}...`);
            
            const result = await api[apiTest.method](...apiTest.params);
            
            if (result.success) {
                console.log(`✅ ${apiTest.name}: 成功`);
                console.log(`   数据获取: 成功`);
                successCount++;
            } else {
                console.log(`❌ ${apiTest.name}: 失败`);
                console.log(`   错误信息: ${result.message}`);
            }
            
        } catch (error) {
            console.log(`❌ ${apiTest.name}: 异常`);
            console.log(`   异常信息: ${error.message}`);
        }
    }
    
    console.log(`\n=== 扩展API测试结果 ===`);
    console.log(`成功: ${successCount}/${totalCount}`);
    console.log(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
    
    if (successCount === totalCount) {
        console.log('🎉 所有扩展API测试通过！');
    } else {
        console.log('⚠️ 部分扩展API测试失败，需要进一步优化');
    }
}

// 测试核心API作为对比
async function testCoreAPIs() {
    console.log('\n=== 测试核心API (对比) ===');
    
    const api = new WutheringWavesAPI();
    
    const coreAPIs = [
        {
            name: '基础信息',
            method: 'getBaseInfo',
            params: [TEST_CONFIG.uid, TEST_CONFIG.token]
        },
        {
            name: '角色信息',
            method: 'getRoleInfo',
            params: [TEST_CONFIG.uid, TEST_CONFIG.token]
        },
        {
            name: '刷新数据',
            method: 'refreshData',
            params: [TEST_CONFIG.uid, TEST_CONFIG.token]
        }
    ];
    
    let successCount = 0;
    let totalCount = coreAPIs.length;
    
    for (const apiTest of coreAPIs) {
        try {
            console.log(`\n测试 ${apiTest.name}...`);
            
            const result = await api[apiTest.method](...apiTest.params);
            
            if (result.success) {
                console.log(`✅ ${apiTest.name}: 成功`);
                successCount++;
            } else {
                console.log(`❌ ${apiTest.name}: 失败`);
                console.log(`   错误信息: ${result.message}`);
            }
            
        } catch (error) {
            console.log(`❌ ${apiTest.name}: 异常`);
            console.log(`   异常信息: ${error.message}`);
        }
    }
    
    console.log(`\n=== 核心API测试结果 ===`);
    console.log(`成功: ${successCount}/${totalCount}`);
    console.log(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
}

// 主测试函数
async function main() {
    console.log('开始优化后的API测试...');
    
    // 先测试核心API确保基础功能正常
    await testCoreAPIs();
    
    // 再测试扩展API
    await testOptimizedExtendedAPIs();
    
    console.log('\n测试完成！');
}

// 运行测试
main().catch(console.error);
