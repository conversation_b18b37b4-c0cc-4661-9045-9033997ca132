import plugin from "../../../lib/plugins/plugin.js"
import roverPlugin from "../lib/index.js"

/**
 * 外部插件示例
 * 演示如何使用Rover基础插件的接口
 */
export class ExternalPluginExample extends plugin {
  constructor() {
    super({
      name: "外部插件示例",
      event: "message",
      priority: 1000,
      rule: [
        {
          reg: "^#示例面板$",
          fnc: "examplePanel",
        },
        {
          reg: "^#示例状态$",
          fnc: "exampleStatus",
        },
        {
          reg: "^#示例探索$",
          fnc: "exampleExplore",
        },
      ],
    })
  }

  /**
   * 示例：获取用户面板信息
   */
  async examplePanel(e) {
    try {
      const userId = e.user_id.toString()
      const botId = e.self_id?.toString() || Bot.uin.toString()

      // 1. 检查用户绑定和登录状态
      const userInfo = await roverPlugin.getUserInfo(userId, botId)
      if (!userInfo.success) {
        await e.reply(`❌ ${userInfo.message}`)
        return false
      }

      if (!userInfo.data.isLoggedIn) {
        await e.reply("❌ 您还未登录，请先使用 #登录 命令")
        return false
      }

      await e.reply("🔄 正在获取面板信息...")

      // 2. 调用API获取基础信息
      const baseInfo = await roverPlugin.callAPI("getBaseInfo", userId, botId)
      if (!baseInfo.success) {
        await e.reply(`❌ 获取基础信息失败: ${baseInfo.message}`)
        return false
      }

      // 3. 调用API获取角色信息
      const roleInfo = await roverPlugin.callAPI("getRoleInfo", userId, botId)
      if (!roleInfo.success) {
        await e.reply(`❌ 获取角色信息失败: ${roleInfo.message}`)
        return false
      }

      // 4. 处理数据并回复
      const { uid } = userInfo.data
      const baseData = baseInfo.data
      const roleData = roleInfo.data

      const message = `
🌊 鸣潮面板信息

👤 基础信息
UID: ${uid}
等级: ${baseData.level || "未知"}
名称: ${baseData.name || "未知"}

🎮 角色信息
角色数量: ${roleData.roleList?.length || 0}
${
  roleData.roleList
    ?.slice(0, 3)
    .map(role => `• ${role.name} Lv.${role.level}`)
    .join("\n") || ""
}
${roleData.roleList?.length > 3 ? "..." : ""}
      `.trim()

      await e.reply(message)
      return true
    } catch (error) {
      console.error("示例面板获取失败:", error)
      await e.reply("❌ 获取面板信息时发生错误")
      return false
    }
  }

  /**
   * 示例：检查用户状态
   */
  async exampleStatus(e) {
    try {
      const userId = e.user_id.toString()
      const botId = e.self_id?.toString() || Bot.uin.toString()

      // 检查绑定状态
      const bindResult = await roverPlugin.checkUserBinding(userId, botId)

      // 检查登录状态
      const loginResult = await roverPlugin.checkUserLogin(userId, botId)

      // 验证cookie有效性
      let cookieValid = false
      if (loginResult.success) {
        const validateResult = await roverPlugin.validateUserCookie(userId, botId)
        cookieValid = validateResult.success && validateResult.valid
      }

      // 获取账号列表
      const accountsResult = await roverPlugin.getUserAccounts(userId, botId)

      const message = `
🔍 用户状态检查

🔗 绑定状态: ${bindResult.success ? `✅ 已绑定 (${bindResult.uid})` : "❌ 未绑定"}
🔑 登录状态: ${loginResult.success ? "✅ 已登录" : "❌ 未登录"}
🍪 Cookie状态: ${cookieValid ? "✅ 有效" : "❌ 无效或已过期"}
📱 账号数量: ${accountsResult.success ? accountsResult.accounts.length : 0}

💡 提示: ${
        bindResult.success && loginResult.success && cookieValid
          ? "状态正常，可以使用所有功能"
          : "请检查绑定和登录状态"
      }
      `.trim()

      await e.reply(message)
      return true
    } catch (error) {
      console.error("状态检查失败:", error)
      await e.reply("❌ 状态检查时发生错误")
      return false
    }
  }

  /**
   * 示例：获取探索数据
   */
  async exampleExplore(e) {
    try {
      const userId = e.user_id.toString()
      const botId = e.self_id?.toString() || Bot.uin.toString()

      // 检查用户状态
      const userInfo = await roverPlugin.getUserInfo(userId, botId)
      if (!userInfo.success || !userInfo.data.isLoggedIn) {
        await e.reply("❌ 请先绑定UID并登录")
        return false
      }

      await e.reply("🔄 正在获取探索数据...")

      // 调用探索数据API
      const exploreResult = await roverPlugin.callAPI("getExploreData", userId, botId, {
        countryCode: "1",
      })

      if (!exploreResult.success) {
        await e.reply(`❌ 获取探索数据失败: ${exploreResult.message}`)
        return false
      }

      const exploreData = exploreResult.data
      const message = `
🗺️ 探索数据

🌍 世界探索度: ${exploreData.worldProgress || "未知"}%
🏆 成就数量: ${exploreData.achievementCount || 0}
📦 宝箱数量: ${exploreData.chestCount || 0}

${
  exploreData.areaList
    ?.slice(0, 3)
    .map(area => `📍 ${area.name}: ${area.progress}%`)
    .join("\n") || ""
}
      `.trim()

      await e.reply(message)
      return true
    } catch (error) {
      console.error("探索数据获取失败:", error)
      await e.reply("❌ 获取探索数据时发生错误")
      return false
    }
  }
}

/**
 * 工具函数示例
 */
export class RoverUtils {
  /**
   * 检查用户是否可以使用功能
   * @param {string} userId
   * @param {string} botId
   * @returns {Promise<{canUse: boolean, message?: string, userInfo?: object}>}
   */
  static async checkUserCanUse(userId, botId) {
    try {
      const userInfo = await roverPlugin.getUserInfo(userId, botId)

      if (!userInfo.success) {
        return {
          canUse: false,
          message: "用户未绑定鸣潮账号，请先使用 #绑定uid 命令",
        }
      }

      if (!userInfo.data.isLoggedIn) {
        return {
          canUse: false,
          message: "用户未登录，请先使用 #登录 命令",
        }
      }

      // 验证cookie有效性
      const validateResult = await roverPlugin.validateUserCookie(userId, botId)
      if (!validateResult.success || !validateResult.valid) {
        return {
          canUse: false,
          message: "登录已过期，请重新登录",
        }
      }

      return {
        canUse: true,
        userInfo: userInfo.data,
      }
    } catch (error) {
      return {
        canUse: false,
        message: "检查用户状态时发生错误",
      }
    }
  }

  /**
   * 安全的API调用包装器
   * @param {string} method
   * @param {string} userId
   * @param {string} botId
   * @param {object} params
   * @returns {Promise<{success: boolean, data?: any, message?: string}>}
   */
  static async safeAPICall(method, userId, botId, params = {}) {
    try {
      // 先检查用户状态
      const checkResult = await this.checkUserCanUse(userId, botId)
      if (!checkResult.canUse) {
        return {
          success: false,
          message: checkResult.message,
        }
      }

      // 调用API
      const result = await roverPlugin.callAPI(method, userId, botId, params)
      return result
    } catch (error) {
      console.error(`API调用失败 [${method}]:`, error)
      return {
        success: false,
        message: `API调用失败: ${error.message}`,
      }
    }
  }

  /**
   * 示例：获取扩展数据（塔楼、活动、商店、邮件）
   */
  static async exampleExtendedData(userId, botId) {
    try {
      // 检查用户状态
      const userInfo = await roverPlugin.getUserInfo(userId, botId)
      if (!userInfo.success || !userInfo.data.isLoggedIn) {
        return { success: false, message: "请先绑定账号并登录" }
      }

      const serverId = "76402e5b20be2c39f095a152090afddc"
      const results = []

      // 获取塔楼数据
      const towerData = await roverPlugin.callAPI("getTowerData", userId, botId, { serverId })
      results.push({
        type: "塔楼数据",
        success: towerData.success,
        message: towerData.success ? "获取成功" : towerData.message,
      })

      // 获取更多活动数据
      const activityData = await roverPlugin.callAPI("getMoreActivityData", userId, botId, {
        serverId,
      })
      results.push({
        type: "更多活动数据",
        success: activityData.success,
        message: activityData.success ? "获取成功" : activityData.message,
      })

      // 获取探索数据
      const exploreData = await roverPlugin.callAPI("getExploreData", userId, botId, { serverId })
      results.push({
        type: "探索数据",
        success: exploreData.success,
        message: exploreData.success ? "获取成功" : exploreData.message,
      })

      // 获取挑战数据
      const challengeData = await roverPlugin.callAPI("getChallengeData", userId, botId, {
        serverId,
      })
      results.push({
        type: "挑战数据",
        success: challengeData.success,
        message: challengeData.success ? "获取成功" : challengeData.message,
      })

      // 获取数据坞信息
      const calabashData = await roverPlugin.callAPI("getCalabashData", userId, botId, { serverId })
      results.push({
        type: "数据坞信息",
        success: calabashData.success,
        message: calabashData.success ? "获取成功" : calabashData.message,
      })

      return {
        success: true,
        data: {
          results,
          towerData: towerData.success ? towerData.data : null,
          activityData: activityData.success ? activityData.data : null,
          shopData: shopData.success ? shopData.data : null,
          mailData: mailData.success ? mailData.data : null,
        },
      }
    } catch (error) {
      console.error("获取扩展数据失败:", error)
      return { success: false, message: `获取扩展数据失败: ${error.message}` }
    }
  }
}
