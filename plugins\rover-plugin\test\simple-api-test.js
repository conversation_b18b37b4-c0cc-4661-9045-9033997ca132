/**
 * 简单API测试 - 使用真实token测试所有API
 */

import { WutheringWavesAPI } from "../components/WutheringWavesAPI.js"

async function runSimpleTest() {
  console.log("🎯 简单API测试 - 使用真实token")
  console.log("=" * 50)

  const api = new WutheringWavesAPI()
  
  // 使用真实的token和UID
  const realUid = "100381247"
  const realToken = "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c"

  console.log(`🔑 使用UID: ${realUid}`)
  console.log(`🔑 使用Token: ${realToken.substring(0, 20)}...`)

  // 测试所有核心API
  const tests = [
    {
      name: "获取基础信息",
      func: () => api.getBaseInfo(realUid, realToken)
    },
    {
      name: "获取角色信息", 
      func: () => api.getRoleInfo(realUid, realToken)
    },
    {
      name: "获取角色详情",
      func: () => api.getRoleDetailInfo("1403", realUid, realToken)
    },
    {
      name: "刷新数据",
      func: () => api.refreshData(realUid, realToken)
    },
    {
      name: "获取实时基础信息",
      func: () => api.getRealtimeBaseInfo(realUid, realToken)
    },
    {
      name: "获取探索数据",
      func: () => api.getExploreData(realUid, realToken)
    },
    {
      name: "获取挑战数据",
      func: () => api.getChallengeData(realUid, realToken)
    },
    {
      name: "获取数据坞信息",
      func: () => api.getCalabashData(realUid, realToken)
    },
    {
      name: "获取塔楼数据",
      func: () => api.getTowerData(realUid, realToken)
    },
    {
      name: "获取更多活动数据",
      func: () => api.getMoreActivityData(realUid, realToken)
    }
  ]

  let successCount = 0
  let riskCount = 0

  for (const test of tests) {
    try {
      console.log(`\n📊 测试: ${test.name}`)
      const result = await test.func()
      
      if (result.success) {
        console.log(`✅ ${test.name} - 成功`)
        successCount++
      } else {
        if (result.message && result.message.includes("风险")) {
          console.log(`⚠️ ${test.name} - 风险检测: ${result.message}`)
          riskCount++
        } else {
          console.log(`❌ ${test.name} - 失败: ${result.message}`)
        }
      }
      
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000))
    } catch (error) {
      console.log(`❌ ${test.name} - 异常: ${error.message}`)
    }
  }

  console.log("\n" + "=" * 50)
  console.log("📊 测试结果汇总")
  console.log("=" * 50)
  console.log(`✅ 成功: ${successCount}/${tests.length}`)
  console.log(`⚠️ 风险检测: ${riskCount}`)
  console.log(`❌ 失败: ${tests.length - successCount - riskCount}`)
  console.log(`🎯 成功率: ${((successCount / tests.length) * 100).toFixed(1)}%`)

  if (successCount >= 5) {
    console.log("\n🎉 核心功能测试通过！插件可以正常使用")
  } else {
    console.log("\n⚠️ 部分核心功能存在问题，需要检查配置")
  }
}

// 运行测试
runSimpleTest().catch(console.error)
