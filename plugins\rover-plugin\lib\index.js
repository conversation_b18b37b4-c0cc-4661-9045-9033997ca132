import roverAPI from "../components/RoverAPI.js"

/**
 * Rover基础插件外部调用接口
 * 为外部插件提供统一的调用入口
 *
 * 使用方法：
 * import { RoverPlugin } from "../../rover-plugin/lib/index.js"
 *
 * const rover = new RoverPlugin()
 * const result = await rover.checkUserBinding(userId, botId)
 */

export class RoverPlugin {
  constructor() {
    this.api = roverAPI
  }

  /**
   * 检查用户是否已绑定鸣潮账号
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @returns {Promise<{success: boolean, uid?: string, message?: string}>}
   *
   * @example
   * const result = await rover.checkUserBinding("*********", "bot_001")
   * if (result.success) {
   *   console.log("用户已绑定UID:", result.uid)
   * } else {
   *   console.log("绑定检查失败:", result.message)
   * }
   */
  async checkUserBinding(userId, botId) {
    return await this.api.checkUserBinding(userId, botId)
  }

  /**
   * 检查用户是否已登录（有有效的cookie）
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @param {string} uid - 鸣潮UID（可选）
   * @returns {Promise<{success: boolean, cookie?: string, message?: string}>}
   *
   * @example
   * const result = await rover.checkUserLogin("*********", "bot_001")
   * if (result.success) {
   *   console.log("用户已登录，cookie:", result.cookie)
   * } else {
   *   console.log("登录检查失败:", result.message)
   * }
   */
  async checkUserLogin(userId, botId, uid = null) {
    return await this.api.checkUserLogin(userId, botId, uid)
  }

  /**
   * 获取用户的完整信息（绑定+登录状态）
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @returns {Promise<{success: boolean, data?: object, message?: string}>}
   *
   * @example
   * const result = await rover.getUserInfo("*********", "bot_001")
   * if (result.success) {
   *   const { uid, isLoggedIn, cookie } = result.data
   *   console.log(`UID: ${uid}, 已登录: ${isLoggedIn}`)
   * }
   */
  async getUserInfo(userId, botId) {
    return await this.api.getUserInfo(userId, botId)
  }

  /**
   * 代理API请求 - 为外部插件提供统一的API调用接口
   * @param {string} method - API方法名
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @param {object} params - API参数
   * @returns {Promise<{success: boolean, data?: any, message?: string}>}
   *
   * @example
   * // 获取基础信息
   * const baseInfo = await rover.callAPI("getBaseInfo", userId, botId, { serverId: "76402e5b20be2c39f095a152090afddc" })
   *
   * // 获取角色信息
   * const roleInfo = await rover.callAPI("getRoleInfo", userId, botId, { serverId: "76402e5b20be2c39f095a152090afddc" })
   *
   * // 获取角色详细信息
   * const roleDetail = await rover.callAPI("getRoleDetailInfo", userId, botId, {
   *   charId: "1102",
   *   serverId: "76402e5b20be2c39f095a152090afddc"
   * })
   *
   * // 获取塔楼数据
   * const towerData = await rover.callAPI("getTowerData", userId, botId, { serverId: "76402e5b20be2c39f095a152090afddc" })
   *
   * // 获取更多活动数据
   * const activityData = await rover.callAPI("getMoreActivityData", userId, botId, { serverId: "76402e5b20be2c39f095a152090afddc" })
   *
   * // 获取探索数据
   * const exploreData = await rover.callAPI("getExploreData", userId, botId, { serverId: "76402e5b20be2c39f095a152090afddc" })
   *
   * // 获取挑战数据
   * const challengeData = await rover.callAPI("getChallengeData", userId, botId, { serverId: "76402e5b20be2c39f095a152090afddc" })
   *
   * // 获取数据坞信息
   * const calabashData = await rover.callAPI("getCalabashData", userId, botId, { serverId: "76402e5b20be2c39f095a152090afddc" })
   */
  async callAPI(method, userId, botId, params = {}) {
    return await this.api.proxyAPIRequest(method, userId, botId, params)
  }

  /**
   * 强制刷新用户的bat token
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @param {string} uid - 鸣潮UID（可选）
   * @returns {Promise<{success: boolean, token?: string, message?: string}>}
   *
   * @example
   * const result = await rover.refreshBatToken("*********", "bot_001")
   * if (result.success) {
   *   console.log("bat token刷新成功:", result.token)
   * }
   */
  async refreshBatToken(userId, botId, uid = null) {
    return await this.api.refreshBatToken(userId, botId, uid)
  }

  /**
   * 验证用户cookie是否有效
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @param {string} uid - 鸣潮UID（可选）
   * @returns {Promise<{success: boolean, valid?: boolean, message?: string}>}
   *
   * @example
   * const result = await rover.validateUserCookie("*********", "bot_001")
   * if (result.success && result.valid) {
   *   console.log("用户cookie有效")
   * } else {
   *   console.log("用户cookie无效或已过期")
   * }
   */
  async validateUserCookie(userId, botId, uid = null) {
    return await this.api.validateUserCookie(userId, botId, uid)
  }

  /**
   * 获取用户的所有绑定账号
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @returns {Promise<{success: boolean, accounts?: Array, message?: string}>}
   *
   * @example
   * const result = await rover.getUserAccounts("*********", "bot_001")
   * if (result.success) {
   *   console.log("用户账号列表:", result.accounts)
   * }
   */
  async getUserAccounts(userId, botId) {
    return await this.api.getUserAccounts(userId, botId)
  }

  /**
   * 获取用户的主账号
   * @param {string} userId - QQ用户ID
   * @param {string} botId - 机器人ID
   * @returns {Promise<{success: boolean, account?: object, message?: string}>}
   *
   * @example
   * const result = await rover.getPrimaryAccount("*********", "bot_001")
   * if (result.success) {
   *   console.log("主账号信息:", result.account)
   * }
   */
  async getPrimaryAccount(userId, botId) {
    return await this.api.getPrimaryAccount(userId, botId)
  }

  /**
   * 数据库操作接口
   * 提供安全的数据库访问方法
   *
   * @example
   * // 获取用户信息
   * const userResult = await rover.database.user.getByUid("*********", "user_001", "bot_001")
   *
   * // 获取绑定UID
   * const bindResult = await rover.database.bind.getUid("user_001", "bot_001")
   */
  get database() {
    return this.api.database
  }

  /**
   * 获取支持的API方法列表
   * @returns {Array<string>}
   *
   * @example
   * const methods = rover.getSupportedMethods()
   * console.log("支持的API方法:", methods)
   */
  getSupportedMethods() {
    return this.api.getSupportedMethods()
  }

  /**
   * 获取插件状态信息
   * @returns {object}
   *
   * @example
   * const status = rover.getStatus()
   * console.log("插件版本:", status.version)
   * console.log("支持的功能:", status.features)
   */
  getStatus() {
    return this.api.getStatus()
  }

  /**
   * 获取原始API实例（高级用法）
   * 注意：直接使用原始API可能绕过安全检查，请谨慎使用
   * @returns {WutheringWavesAPI}
   *
   * @example
   * const rawAPI = rover.getRawAPI()
   * // 直接调用原始API方法
   * const result = await rawAPI.getBaseInfo(uid, cookie, serverId)
   */
  getRawAPI() {
    return this.api.getAPIInstance()
  }
}

// 创建默认实例
const roverPlugin = new RoverPlugin()

// 导出默认实例和类
export default roverPlugin
export { roverAPI as RoverAPI }
