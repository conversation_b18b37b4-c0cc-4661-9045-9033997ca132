import axios from "axios"
import { HttpsProxyAgent } from "https-proxy-agent"

// 测试配置
const TEST_CONFIG = {
  uid: "100381247",
  token:
    "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  proxy: "http://1907581050:<EMAIL>:9151",
}

// WutheringWavesUID 风格的请求头
function getWutheringWavesUIDHeaders(platform = "ios") {
  const devCode = generateRandomString()
  const headers = {
    source: platform,
    "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
    devCode: devCode,
    version: "2.5.0",
    token: TEST_CONFIG.token,
  }

  if (platform === "ios") {
    headers["User-Agent"] =
      "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
  } else {
    headers["User-Agent"] =
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
  }

  return headers
}

function generateRandomString(length = 32) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  let result = ""
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 获取 bat token (模拟 WutheringWavesUID 的方式)
async function getBatToken(uid, token) {
  const proxyAgent = new HttpsProxyAgent(TEST_CONFIG.proxy)

  try {
    const headers = getWutheringWavesUIDHeaders("ios")
    headers["b-at"] = "" // 清空 b-at

    const data = {
      serverId: "76402e5b20be2c39f095a152090afddc",
      roleId: uid,
    }

    const response = await axios.post("https://api.kurobbs.com/aki/roleBox/requestToken", data, {
      headers,
      httpsAgent: proxyAgent,
      timeout: 10000,
    })

    if (response.data.code === 200 || response.data.code === 10902) {
      let accessToken = ""
      const contentData = response.data.data

      if (typeof contentData === "string") {
        try {
          const jsonData = JSON.parse(contentData)
          accessToken = jsonData.accessToken || ""
        } catch (e) {
          console.log("解析 bat token 失败:", e.message)
        }
      } else if (typeof contentData === "object") {
        accessToken = contentData.accessToken || ""
      }

      return accessToken
    }

    return null
  } catch (error) {
    console.log("获取 bat token 失败:", error.message)
    return null
  }
}

// 测试扩展API - WutheringWavesUID风格
async function testWutheringWavesUIDStyle() {
  console.log("\n=== 测试 WutheringWavesUID 风格的API调用 ===")

  // 首先获取 bat token
  console.log("获取 bat token...")
  const batToken = await getBatToken(TEST_CONFIG.uid, TEST_CONFIG.token)
  if (batToken) {
    console.log("✅ bat token 获取成功")
  } else {
    console.log("❌ bat token 获取失败，继续测试...")
  }

  const proxyAgent = new HttpsProxyAgent(TEST_CONFIG.proxy)

  const testAPIs = [
    {
      name: "探索数据 (exploreIndex)",
      url: "https://api.kurobbs.com/aki/roleBox/akiBox/exploreIndex",
      data: {
        gameId: 3,
        serverId: "76402e5b20be2c39f095a152090afddc",
        roleId: TEST_CONFIG.uid,
        countryCode: "1",
      },
    },
    {
      name: "挑战数据 (challengeDetails)",
      url: "https://api.kurobbs.com/aki/roleBox/akiBox/challengeDetails",
      data: {
        gameId: 3,
        serverId: "76402e5b20be2c39f095a152090afddc",
        roleId: TEST_CONFIG.uid,
      },
    },
    {
      name: "数据坞 (calabashData)",
      url: "https://api.kurobbs.com/aki/roleBox/akiBox/calabashData",
      data: {
        gameId: 3,
        serverId: "76402e5b20be2c39f095a152090afddc",
        roleId: TEST_CONFIG.uid,
      },
    },
    {
      name: "深渊数据 (towerIndex)",
      url: "https://api.kurobbs.com/aki/roleBox/akiBox/towerIndex",
      data: {
        gameId: 3,
        serverId: "76402e5b20be2c39f095a152090afddc",
        roleId: TEST_CONFIG.uid,
      },
    },
    {
      name: "更多活动 (moreActivity)",
      url: "https://api.kurobbs.com/aki/roleBox/akiBox/moreActivity",
      data: {
        gameId: 3,
        serverId: "76402e5b20be2c39f095a152090afddc",
        roleId: TEST_CONFIG.uid,
      },
    },
  ]

  for (const api of testAPIs) {
    try {
      console.log(`\n测试 ${api.name}...`)

      // 使用 iOS 平台的请求头
      const headers = getWutheringWavesUIDHeaders("ios")

      // 如果有 bat token，添加到请求头
      if (batToken) {
        headers["b-at"] = batToken
      }

      const response = await axios.post(api.url, api.data, {
        headers,
        httpsAgent: proxyAgent,
        timeout: 10000,
      })

      console.log(`✅ ${api.name}: 成功`)
      console.log(`   状态码: ${response.status}`)
      console.log(`   响应码: ${response.data.code}`)
      console.log(`   消息: ${response.data.msg || "N/A"}`)

      if (response.data.code === 200 || response.data.code === 10902) {
        console.log(`   数据: 获取成功`)
      }
    } catch (error) {
      console.log(`❌ ${api.name}: 失败`)
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`)
        console.log(`   响应: ${JSON.stringify(error.response.data)}`)
      } else {
        console.log(`   错误: ${error.message}`)
      }
    }
  }
}

// 测试不同平台的请求头
async function testDifferentPlatforms() {
  console.log("\n=== 测试不同平台的请求头 ===")

  const proxyAgent = new HttpsProxyAgent(TEST_CONFIG.proxy)
  const platforms = ["ios", "h5"]

  const testAPI = {
    name: "探索数据",
    url: "https://api.kurobbs.com/aki/roleBox/akiBox/exploreIndex",
    data: {
      gameId: 3,
      serverId: "76402e5b20be2c39f095a152090afddc",
      roleId: TEST_CONFIG.uid,
      countryCode: "1",
    },
  }

  for (const platform of platforms) {
    try {
      console.log(`\n测试平台: ${platform}`)

      const headers = getWutheringWavesUIDHeaders(platform)

      const response = await axios.post(testAPI.url, testAPI.data, {
        headers,
        httpsAgent: proxyAgent,
        timeout: 10000,
      })

      console.log(`✅ 平台 ${platform}: 成功`)
      console.log(`   响应码: ${response.data.code}`)
      console.log(`   消息: ${response.data.msg || "N/A"}`)
    } catch (error) {
      console.log(`❌ 平台 ${platform}: 失败`)
      if (error.response) {
        console.log(`   响应码: ${error.response.data.code}`)
        console.log(`   消息: ${error.response.data.msg || "N/A"}`)
      } else {
        console.log(`   错误: ${error.message}`)
      }
    }
  }
}

// 主测试函数
async function main() {
  console.log("开始 WutheringWavesUID 风格的API测试...")
  console.log(`使用 UID: ${TEST_CONFIG.uid}`)
  console.log(`使用代理: ${TEST_CONFIG.proxy}`)

  await testWutheringWavesUIDStyle()
  await testDifferentPlatforms()

  console.log("\n测试完成！")
}

// 直接运行主函数
main().catch(console.error)
